#!/bin/bash

# UnRen Linux Launcher Script
# This script launches the PowerShell version of UnRen on Linux

# Check if PowerShell is installed
if ! command -v pwsh &> /dev/null; then
    echo "PowerShell Core (pwsh) is not installed."
    echo "Please install PowerShell Core:"
    echo "  Ubuntu/Debian: sudo apt install powershell"
    echo "  Or download from: https://github.com/PowerShell/PowerShell/releases"
    exit 1
fi

# Check if Python is available
if ! command -v python3 &> /dev/null && ! command -v python &> /dev/null; then
    echo "Python is not installed or not in PATH."
    echo "Please install Python 3:"
    echo "  Ubuntu/Debian: sudo apt install python3"
    exit 1
fi

# Check if we're in the right directory
if [ ! -d "game" ] || [ ! -d "lib" ] || [ ! -d "renpy" ]; then
    echo "Error: This script must be run from the app/ folder containing:"
    echo "  - game/ folder"
    echo "  - lib/ folder" 
    echo "  - renpy/ folder"
    echo ""
    echo "Current directory contents:"
    ls -la
    exit 1
fi

echo "Starting UnRen for Linux..."
echo "Running from: $(pwd)"
echo ""

# Launch the PowerShell script
pwsh -File "./UnRen-Powershell-forall.ps1"
