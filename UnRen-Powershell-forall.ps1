function final {
    Write-Host ""
    Write-Host -NoNewline "Enter 1 to go back to the menu, or any other key to exit: "
    $key = Read-Host
    if($key -eq 1) { menu }
    Exit
}

function extract {
    Write-Host ""
    Write-Host "   Creating rpatool..."
    New-Item -ItemType "file" -Path "$currentdir\rpatool.py" | Out-Null
    [IO.File]::WriteAllBytes("$currentdir\rpatool.py", [Convert]::FromBase64String($rpatool))

    Set-Location -Path $gamedir

    Write-Host ""
    Write-Host "   Remove RPA archives after extraction?"
    Write-Host -NoNewline "    Enter (y/n)"
    [char]$key = Read-Host
    if ($key -eq "y") {
        Write-Host "   + RPA archives will be deleted"
        Write-Host ""
    } else {
        Write-Host "   + RPA archives won't be deleted"
        Write-Host ""
    }

    if (Test-Path "$pythondir\Lib")
    {
        if($key -eq "y"){
            & "$pythondir\python.exe" "-O" "$currentdir\rpatool.py" "-r" "$gamedir"
        }
        else {
            & "$pythondir\python.exe" "-O" "$currentdir\rpatool.py" "$gamedir"
        }
    } else {
        if($key -eq "y"){
            & "$pythondir\python.exe" "$currentdir\rpatool.py" "-r" "$gamedir"
        }
        else {
            & "$pythondir\python.exe" "$currentdir\rpatool.py" "$gamedir"
        }
    }

    Remove-Item -Path "$currentdir\rpatool.py"
    if(Test-Path "$currentdir\rpatool.pyo") { Remove-Item -Path "$currentdir\rpatool.pyo" }
    if(Test-Path "$currentdir\__pycache__") { Remove-Item -Path "$currentdir\__pycache__" -Recurse }
    if(($option -ne 8) -and ($option -ne 9)){
        final
    }
}

#
function decompile {

    Write-Host ""
    Write-Host "   Creating unrpyc..."
    Add-Type -AssemblyName "System.IO.Compression.FileSystem"

    New-Item -ItemType "file" -Path "$currentdir\unrpyc.zip" | Out-Null

    [IO.File]::WriteAllBytes("$currentdir\unrpyc.zip", [Convert]::FromBase64String($decompcab))

    $SourceZipFile = "$currentdir\unrpyc.zip"
    $TargetFolder = "$currentdir\"
    [IO.Compression.ZipFile]::ExtractToDirectory($SourceZipFile, $TargetFolder)
    Remove-Item -Path "$currentdir\unrpyc.zip"

    Set-Location -Path $gamedir
    if (Test-Path "$pythondir\Lib")
    {
        & "$pythondir\python.exe" "-O" "$currentdir\unrpyc.py" "--init-offset" $gamedir
        Write-Host ""
    } else {
        & "$pythondir\python.exe" "$currentdir\unrpyc.py" "--init-offset" $gamedir
        Write-Host ""
    }

    Remove-Item -Path "$currentdir\unrpyc.py"
    Remove-Item -Path "$currentdir\deobfuscate.py"
    Remove-Item -Path "$currentdir\decompiler" -Recurse
    if(Test-Path "$currentdir\unrpyc.pyo") { Remove-Item -Path "$currentdir\unrpyc.pyo" }
    if(Test-Path "$currentdir\deobfuscate.pyo") { Remove-Item -Path "$currentdir\deobfuscate.pyo" }
    if(Test-Path "$currentdir\__pycache__") { Remove-Item -Path "$currentdir\__pycache__" -Recurse }
    Write-Host "All Scripts are decompiled"
    if($option -ne 9){
        final
    }
}

#Drop our console/dev mode enabler into the game folder
function console {
    Write-Host ""
    Write-Host "   Creating Developer/Console file..."
    New-Item -ItemType "file" -Path "$gamedir\unren-console.rpy" -Force | Out-Null
    [IO.File]::WriteAllBytes("$gamedir\unren-console.rpy", [Convert]::FromBase64String($unren_console))

    Write-Host "    + Console: SHIFT+O"
    Write-Host "    + Dev Menu: SHIFT+D"
    if($option -ne 9){
        final
    }
}

#Drop our Quick Save/Load file into the game folder
function quick {
    Write-Host ""
    Write-Host "   Creating unren-quick.rpy..."
    New-Item -ItemType "file" -Path "$gamedir\unren-quick.rpy" -Force | Out-Null
    [IO.File]::WriteAllBytes("$gamedir\unren-quick.rpy", [Convert]::FromBase64String($unren_quick))

    Write-Host "    Default hotkeys:"
    Write-Host "    + Quick Save: F5"
    Write-Host "    + Quick Load: F9"
    if($option -ne 9){
        final
    }
}

#Drop our skip file into the game folder
function skip {
    Write-Host ""
    Write-Host "   Creating skip file..."
    New-Item -ItemType "file" -Path "$gamedir\unren-skip.rpy" -Force | Out-Null
    [IO.File]::WriteAllBytes("$gamedir\unren-skip.rpy", [Convert]::FromBase64String($unren_skip))

    Write-Host "    + You can now skip all text using TAB and CTRL keys"
    if($option -ne 9){
        final
    }
}

#Drop our skip file into the game folder
function rollback {
    Write-Host "   Creating rollback file..."
    New-Item -ItemType "file" -Path "$gamedir\unren-rollback.rpy" -Force | Out-Null
    [IO.File]::WriteAllBytes("$gamedir\unren-rollback.rpy", [Convert]::FromBase64String($unren_rollback))

    Write-Host "    + You can now rollback using the scrollwheel"
    final
}

function menu {
    Write-Host ""
    Write-Host "   Available Options:"
    Write-Host "     1) Extract RPA packages"
    Write-Host "     2) Decompile rpyc files"
    Write-Host "     3) Enable Console and Developer Menu"
    Write-Host "     4) Enable Quick Save and Quick Load"
    Write-Host "     5) Force enable skipping of unseen content"
    Write-Host "     6) Force enable rollback (scroll wheel)"
    Write-Host "     8) Extract and Decompile"
    Write-Host "     9) All of the above"
    Write-Host ""
    Write-Host -NoNewline "Enter a number:"
    $option = Read-Host
    switch ($option) {
        1 { extract }
        2 { decompile }
        3 { console }
        4 { quick }
        5 { skip }
        6 { rollback }
        8 {
            extract
            decompile
        }
        9 {
            extract
            decompile
            console
            quick
            skip
            rollback
        }
        Default
        {
            final
        }
    }
}

<#
--------------------------------------------------------------------------------
Configuration:
Set a Quick Save and Quick Load hotkey - http://www.pygame.org/docs/ref/key.html
--------------------------------------------------------------------------------
!! END CONFIG !!
--------------------------------------------------------------------------------
The following variables are Base64 encoded strings for unrpyc and rpatool
Due to batch limitations on variable lengths, they need to be split into
multiple variables, and joined later using powershell.
--------------------------------------------------------------------------------
unrpyc by CensoredUsername
https://github.com/CensoredUsername/unrpyc
--------------------------------------------------------------------------------
#>

[string]$rpatool = "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"


[string] $decompcab = "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"

<#
The file for enabling the console and developer mode.
#>

[string]$unren_console = "aW5pdCA5OTkgcHl0aG9uOg0KICAgIGNvbmZpZy5kZXZlbG9wZXIgPSBUcnVlDQogICAgY29uZmlnLmNvbnNvbGUgPSBUcnVl"

<#
The file for enabling the Quick Save/Load.
#>

[string]$unren_quick = "aW5pdCA5OTkgcHl0aG9uOg0KICAgIHRyeToNCiAgICAgICAgY29uZmlnLnVuZGVybGF5WzBdLmtleW1hcFsncXVpY2tTYXZlJ10gPSBRdWlja1NhdmUoKQ0KICAgICAgICBjb25maWcua2V5bWFwWydxdWlja1NhdmUnXSA9ICdLX0Y1Jw0KICAgICAgICBjb25maWcudW5kZXJsYXlbMF0ua2V5bWFwWydxdWlja0xvYWQnXSA9IFF1aWNrTG9hZCgpDQogICAgICAgIGNvbmZpZy5rZXltYXBbJ3F1aWNrTG9hZCddID0gJ0tfRjknDQogICAgZXhjZXB0Og0KICAgICAgICBwYXNz"

<#
The file for enabling skip.
#>

[string]$unren_skip = "aW5pdCA5OTkgcHl0aG9uOg0KICAgIF9wcmVmZXJlbmNlcy5za2lwX3Vuc2VlbiA9IFRydWUNCiAgICByZW5weS5nYW1lLnByZWZlcmVuY2VzLnNraXBfdW5zZWVuID0gVHJ1ZQ0KICAgIHJlbnB5LmNvbmZpZy5hbGxvd19za2lwcGluZyA9IFRydWUNCiAgICByZW5weS5jb25maWcuZmFzdF9za2lwcGluZyA9IFRydWUNCiAgICB0cnk6DQogICAgICAgIGNvbmZpZy5rZXltYXBbJ3NraXAnXSA9IFsgJ0tfTENUUkwnLCAnS19SQ1RSTCcgXQ0KICAgIGV4Y2VwdDoNCiAgICAgICAgcGFzcw0K"

<#
The file for enabling rollback.
#>

[string]$unren_rollback = "aW5pdCA5OTkgcHl0aG9uOg0KICAgIHJlbnB5LmNvbmZpZy5yb2xsYmFja19lbmFibGVkID0gVHJ1ZQ0KICAgIHJlbnB5LmNvbmZpZy5oYXJkX3JvbGxiYWNrX2xpbWl0ID0gMjU2DQogICAgcmVucHkuY29uZmlnLnJvbGxiYWNrX2xlbmd0aCA9IDI1Ng0KICAgIGRlZiB1bnJlbl9ub2Jsb2NrKCAqYXJncywgKiprd2FyZ3MgKToNCiAgICAgICAgcmV0dXJuDQogICAgcmVucHkuYmxvY2tfcm9sbGJhY2sgPSB1bnJlbl9ub2Jsb2NrDQogICAgdHJ5Og0KICAgICAgICBjb25maWcua2V5bWFwWydyb2xsYmFjayddID0gWyAnS19QQUdFVVAnLCAncmVwZWF0X0tfUEFHRVVQJywgJ0tfQUNfQkFDSycsICdtb3VzZWRvd25fNCcgXQ0KICAgIGV4Y2VwdDoNCiAgICAgICAgcGFzcw=="

<#
--------------------------------------------------------------------------------
!! DO NOT EDIT BELOW THIS LINE !!
--------------------------------------------------------------------------------
#>

[string]$version = "version=PowerShell-forall(v9.4) (240216)"
[System.Console]::Title = "UnRen.bat $version"
[int] $option = 0
<#
--------------------------------------------------------------------------------
Splash screen
--------------------------------------------------------------------------------
#>

Clear-Host
Write-Host "     __  __      ____               __          __"
Write-Host "    / / / /___  / __ \___  ____    / /_  ____ _/ /_"
Write-Host "   / / / / __ \/ /_/ / _ \/ __ \  / __ \/ __ /  __/"
Write-Host "  / /_/ / / / / _ ,_/| __/ / / / / /_/ / /_/ / /_"
Write-Host "  \____/_/ /_/_/ \_\ \__/_/ /_(_)_.___/\__,_/\__/ $version"
Write-Host "   Gideon"
Write-Host ""
Write-Host "  ----------------------------------------------------"

<#
--------------------------------------------------------------------------------
Set our paths, and make sure we can find python exe
--------------------------------------------------------------------------------
#>
Write-Host "Enter the path to the game, drag'n'drop it or press enter immediately if this tool is already in the desired folder:"
[string]$currentdir = Read-Host
if ([string]::IsNullOrEmpty($currentdir))
{
    $currentdir = $PWD.Path
}
$currentdir = $currentdir -replace '"',''
Write-Host $currentdir

if ((Test-Path "$currentdir\lib\windows-x86_64\python.exe") -and ([Environment]::Is64BitOperatingSystem -eq $true))
{
    [string]$pythondir = "$currentdir\lib\windows-x86_64"
} elseif (Test-Path "$currentdir\lib\windows-i686\python.exe")
{
    [string]$pythondir = "$currentdir\lib\windows-i686"
}

if ((Test-Path "$currentdir\lib\py2-windows-x86_64\python.exe") -and ([Environment]::Is64BitOperatingSystem -eq $true))
{
    [string]$pythondir = "$currentdir\lib\py2-windows-x86_64"
} elseif (Test-Path "$currentdir\lib\py2-windows-i686\python.exe")
{
    [string]$pythondir = "$currentdir\lib\py2-windows-i686"
}

if ((Test-Path "$currentdir\lib\py3-windows-x86_64\python.exe") -and ([Environment]::Is64BitOperatingSystem -eq $true))
{
    [string]$pythondir = "$currentdir\lib\py3-windows-x86_64"
} elseif (Test-Path "$currentdir\lib\py3-windows-i686\python.exe")
{
    [string]$pythondir = "$currentdir\lib\py3-windows-i686"
}

if ($null -eq $pythondir)
{
    Write-Host "! Error: Cannot locate python directory, unable to continue."
    Write-Host "  Are you sure we're in the game's root directory?"
    Write-Host ""
    Write-Host "  Press any key to exit..."
    Read-Host
    Exit
}

if ((Test-Path "$currentdir\renpy") -and (Test-Path "$currentdir\game"))
{
    [string]$gamedir = "$currentdir\game\"
} else {
    Write-Host "! Error: Cannot locate game directory, unable to continue."
    Write-Host "  Are you sure we're in the game's root directory?"
    Write-Host ""
    Write-Host "  Press any key to exit..."
    Read-Host
    Exit
}

$Env:PYTHONHOME=$pythondir
if (Test-Path "$currentdir\lib\pythonlib2.7"){
    $Env:PYTHONPATH="$currentdir\lib\pythonlib2.7"
} elseif (Test-Path "$currentdir\lib\python2.7") {
    $Env:PYTHONPATH="$currentdir\lib\python2.7"
} elseif (Test-Path "$currentdir\lib\python3.9") {
    $Env:PYTHONPATH="$currentdir\lib\python3.9"
}

menu
