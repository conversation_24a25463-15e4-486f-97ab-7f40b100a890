@echo off
SETLOCAL EnableDelayedExpansion EnableExtensions
REM --------------------------------------------------------------------------------
REM Configuration:
REM   Set a Quick Save and Quick Load hotkey - http://www.pygame.org/docs/ref/key.html
REM --------------------------------------------------------------------------------
set "quicksavekey=K_F5"
set "quickloadkey=K_F9"
REM --------------------------------------------------------------------------------
REM !! END CONFIG !!
REM --------------------------------------------------------------------------------
REM The following variables are Base64 encoded strings for unrpyc and rpatool
REM Due to batch limitations on variable lengths, they need to be split into
REM multiple variables, and joined later using powershell.
REM --------------------------------------------------------------------------------
REM unrpyc by CensoredUsername
REM   https://github.com/CensoredUsername/unrpyc
REM Edited to remove multiprocessing and adjust output spacing 44febb0 2019-10-07T07:06:47.000Z
REM   https://github.com/F95Sam/unrpyc
REM --------------------------------------------------------------------------------
set decompcab01=TVNDRgAAAAB/1wAAAAAAADAYAAAAAAAAAwEBAAsABAAMNwAAABgAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA
set decompcab02=AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAHQZAAAHAAEATJcAAAAAAAAAAFZSDDAAAF9faW5pdF9fLnB5AIwsAABMlwAAAABWUgwwAABhc3RkdW1wLnB5AKuTAADYwwAAAABWUgwwAABjb2RlZ2VuLnB5AFcoAACDVwEAAABWUgwwIABkZW9iZnVzY2F0ZS5weQCTbAAA2n8BAAAAVlIMMAAAbWFnaWMucHkAmHcAAG3sAQAAAFZSDDAAAHNjcmVlbmRlY29tcGlsZXIucHkASUQAAAVkAgAAAFZSDDAAAHNsMmRlY29tcGlsZXIucHkAYBQAAE6oAgAAAFZSDDAAAHRlc3RjYXNlZGVjb21waWxlci5weQCKFQAArrwCAAAAVlIMMAAAdHJhbnNsYXRlLnB5AEsyAAA40gIAAABWUkOxIAB1bnJweWMucHkAhDwAAIMEAwAAAFZSDDAAAHV0aWwucHkASZVU6y4fAIBDS+w8/XPbNpa/66/A0pOxNGUUO2mzE8965xRbbrR1bI+tbK6TejQUCUlsKJLlhxVtp//7vfcAkABISo7Tu72bue5Oa4HAw8PD+8YDDthZkm6zcLkqWN8fsJdHxy/Zz2UWsp+G7M5fRTyPedY76B2wG56twzwPk5iFOVvxjM+3bJl5ccEDly0yzlmyYP7Ky5bcZUXCvHjLUp7lMCCZF14Yh/GSecyHCQEc9C1WAChPFsXGyzh0D5iX54kfegCRBYlfrnlceAXOuAgBE9YvVpw5d3KEM6BpAu5FAC+MGX5VH9kmLFZJWbCM50UW+gjFhU5+VAaIh/ochetQzoHDiRI5gAPAZQ7rQGxdtk6CcIH/5bS4tJxHYb5yWRAi8HlZQGOOjT6PcRSs5UWSsZxHiBrACAF7WnGNIfXCeVIkbCFJlWPLZpWszdWEiNOizGKYltOoIAHS0ay/cr/AFhywSKIo2eAC/SQOQlxXfkLbN4Wv3jx54LQkseNxUgDGAg/ci7TeYvkpX3lRxOZcUg6mDmMAho1qVRnikBfAB6EXsTTJaFJ7tUOBxLsxu7u+mH4c3Y7Z5I7d3F7/c3I+PmfO6A5+Oy77OJm+u/4wZdDjdnQ1/ZldX7DR1c/sp8nVucvG/3lzO767Y9e3AGzy/uZyMobWydXZ5YfzydWP7C2MvLqessvJ+8kUwE6vaUoJbDK+Q3Dvx7dn7+Dn6O3kcjL92QVQF5PpFcK9uL5lI3Yzup1Ozj5cjm7ZzYfbm+u7MaBwDoCvJlcXtzDP+P34ajqEeaGNjf8JP9jdu9HlJU4G0EYfYA23iCU7u775+Xby47spe3d9eT6GxrdjwG709nIsJoOlnV2OJu9ddj56P/pxTKOuAQ6uEDsKHNnHd2NsxDlH8P+z6eT6Chdzdn01vYWfLqz1dloN/ji5G7tsdDu5Q7Jc3F6/x2UiYWHMNYGBkVdjAQeJbu4NdMHfH+7GFUh2Ph5dArQ7HCwWqroPe71FBiw7my3Kosz4bMbCNXICK+PQTwI+i8KC
set decompcab03=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
set decompcab04=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
set decompcab05=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
set decompcab06=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
set decompcab07=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
set decompcab08=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
set decompcab09=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
set decompcab10=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
set decompcab11=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
set decompcab12=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
set decompcab13=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
set decompcab14=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
set decompcab15=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
set decompcab16=N195uvHMkL3Z+Rf4WS0Sf+mldiZuuLtOXeGABurfD+0A4I+jC+CpkP9ozQsHun0c2ADRLjnqR97gYdut4K4PeHpy7M2ng+mCU3SZmh0g071r11YP1ux24vQfQAiQ2R7eIHzWqIalYtPv8vVihkRB9+BB4cML+1DwzUv5Bcn6snWWkIe0/KZD6KwmC+eYWq4bndT/pGJZlnAGjsExehwKR3gFbnx47tvjrd/lN4GJsQojLKKWTnvESWldc51R52t3z1fUgqQzfl8YAc4fZMxnQkwVICvzSmogecxz4XgMzQ5UT2HifV7w86LncSivh9ZzxeOrRYXQf3U5DJKADYD4UzsGT/UavFCRkXsMBPtqTb7NHlNTJJ0jV6xaxBKeB2tdi8yypVsXFINVt16n27vSZ82VR431RleLI+BRI0wK14H2loTpNW4r6EJW2637OsFtVVZZ9Tj1dud73NuxtjF/EyY9+PjAmSwBBY4WB1xP+U7ATbJccIzUpnZ9GLpbmCc0Wzv50akIKnz1stnacOC8dl78i1K3DpP2Oi7Wd1PvK3gSJn1rjNd6V1m7NdiLrH/bN9VeJw0bQOJs/sAy9/8X
REM --------------------------------------------------------------------------------
REM unrpyc by CensoredUsername 2f9810c 2022-03-31 v1.1.8 (Python 2)
REM   https://github.com/CensoredUsername/unrpyc
REM fried added proposed changes, as compiled by sigio, for improved Ren'Py 7.5+ compatibility
REM   https://github.com/CensoredUsername/unrpyc/pull/154/files
REM fried also added some resiliency when brackets are in pathnames
REM --------------------------------------------------------------------------------
set decompcab20=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
set decompcab21=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
set decompcab22=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
set decompcab23=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
set decompcab24=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
set decompcab25=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
set decompcab26=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**************************/0HUGlK+nh4aKRXNPvijjhuf96Bfk1V3C3nfGcRMgIOiwhuYzDOYP7VarguIDAVFF4KAUesI4TbDfCYd2t3yDpEHxGaOThVNCfKEmFvrOT3nE+QltgDH2G/gS9vq+UxdmA7QrAwgxZSXmFbKtuAmNretuBzt9sUY7NdbMZAEP7UvtpU0Y9LfZjWb2aG6U8pjqZ8WYLUY1wlgfVo1acD1mpydhtWTNPkEKaN2RA0udAuuJWBMEhjcjTJeD3GZhEHhfpcxInQsDrWtI4HyglXsGN3O9T7DVryT4nectPiHj3c5VqhR7MZECnZl62NsrDTQlRIX92usyOnlAIXxBleXRnf3CJuP5+583RUC7f94rq/u9u029lpVdVrsEytxlW9oB8ulDf5ms34exzXzBPP7hmJXx2zSyfataYRTxD059AJwU1YBdo33I3wTaC4+GPxdRVLpe3u729nXkuV79ijgZjQmxYd8ugLCo6i6glG0sIxaCxMtaNeTrzGYORtx+mmRhvNSpmzOJau7sBzk8+JPIIM3rDu7iH8RG8k2LvVhLLqIj6EaJQB6LE5cC0Ej86XtdfgxQLC/LWjr+keOWYAdcBx2FMCR0mHYFDCU6891fODzoLV6IPGYUR8yHaE8MhYesbXGDyM/cYw4S5q1a2JZkvZt9BTUsk67V77Co5FQ9CJP3XLFqOT6COQPBTbPcAcqDfrqj45qJ/wdszJMC2FZGJesvEGT0Gg7Qg8oqnYAVfnF8XrjuMqaMcyTk6zTG1bROog
set decompcab27=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
set decompcab28=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
set decompcab29=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
set decompcab30=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
set decompcab31=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
set decompcab32=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
set decompcab33=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
REM --------------------------------------------------------------------------------
REM unrpyc by madeddy, 8 & 7.5 compat_fix@1.1.8 (Python 2)
REM   https://github.com/madeddy/unrpyc/tree/Renpy_8_7.5_compat_fix%40v1.1.8
REM --------------------------------------------------------------------------------
REM set decompcab20=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
REM set decompcab21=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
REM set decompcab22=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
REM set decompcab23=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
REM set decompcab24=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
REM set decompcab25=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
REM set decompcab26=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
REM set decompcab27=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
REM set decompcab28=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
REM set decompcab29=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
REM set decompcab30=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
REM set decompcab31=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
REM set decompcab32=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
REM set decompcab33=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 
REM --------------------------------------------------------------------------------
REM unrpyc by madeddy, mangled by fried (Python 3)
REM   https://github.com/madeddy/unrpyc/tree/py3_v1.1.8
REM --------------------------------------------------------------------------------
REM set decompcab40=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
REM set decompcab41=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
REM set decompcab42=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
REM set decompcab43=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
REM set decompcab44=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
REM set decompcab45=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
REM set decompcab46=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
REM set decompcab47=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
REM set decompcab48=y/6Wlxxqdyu5XKD/6JXV93jHkGeYeoH2EVOWwbmU9kxSVBXCPnKhbfT/e13AXiF8DcB6Kr0bP2H3NI7gcQG6Jp+X2gM0RDJx0l9divLag5FXUcxN+/AROSRGTcF0SXBGwgri8Q4ggDC4A9nkTmDZvCheFvXkfgIYmXdqPnetAFN10yyRcJ/sqDUl9EssmxcFVDxfbfY2Rd7L4/xOcXswxmCaF8wIMyvZ0MVLwW4BoIAnX9zASsxwjGazYkgTHvqVmHULZ9fA4CxKmN5cv/q9Vx5LHxrvvXyeoDea/mfFkGAExNhnnMEZMS6o0k2lJG2ynqjV7T9C9qWEgZBIh6mhZIV3U0T1V02ivdOv9t+SJiewMr0REwPFu1TS4VodUMV11ekDIEboRRMCJjea4ovygnkCE8TB2Ms+zV5bOqtlDvIyopuY3RVf9e4TodajuEaQZZi8jKYR/NG9HqRGY7xku9cYfiQqiV7XzZHgrLTEXCA/dMGrGmu/MC1eRqToBwK096oBDzxkBFmCEKDUPRbFncTDGc9iTj0PPioGsOd5jvMn9qVIMMyfC8VSg1Ohfkuyy5ziktXpzjPvv+mwwVg6cTgTJ1tYsZK/H9ot1LW1Whxiy4G13LBwxDSDv4C1Xqvx/bM2h25mJtZfCKyYyJHdLEO3+hfpYQQtRPbJMr2TgjpadZgT232RMJ4M6UDu4vYN+x6jcMRBull2KdIMAoqDCpYw78cECtXQzq6di5kFAyD1EGOk4jciZWCi4rDpI3uaEc8XXS3+od6bJz0ob2yGtoP8hfqOMhzz2afhxdKJkU+55WFSZTGWAkeKYyY+wJrgg4+wj6SinMusifQq4jQUL4FJNvuTW9Ibq98AbNSifQT4zO4gAfHRYCiLiMkeARF3XS9P/60JEVgPwCMqJoA2hv1p2bjCKfkiRSFBQprHtRSv03Li+6WTDvN2Pf/WCnDGdMivYbb7Dm2Iqt/ST5KRL0d1ZoulyhCotfNNLvBofY+OkW7LeRev1CYqjuYsg68dK23mI5UBv+ZCcRRaA+YhNhXJATF0ga3z2s4ul9TVzbY1zrlT+sPaZN9UxZ+7IySLwJLCPq5qBImgpBZtbBQILZwYw7Xg58394bFQi7qrH5sgl8jdjbHZM8iCVbrKKElwsNvxAHwFP9Vb8LtfF+/WD2phQLScZ1bUGBFG//PPER2jvymKvN0f1pgAOHEWNNXLTlNjIO7buVWX2FUwR38+df9HxJ84MvLMacm9w/gGahkdzO1eHW2G7f6x01HyJiLfloQ1teLxl90GjCVqM8j/tm0elaDcbJVyJYkB3rFKrVcFKm29j0fVpK9R0NzdG/svDPCdGDWphApK7lb3J5uueVtbrLsdmobqZV8IffetHM/V5NhPSkuoif0US6c2omNZUA972B08wDwItzNmGUAIB9gj43M7fmjoFhPoicxrk+QZkr7A2aOh7kCKg8A7tBC0q8aDfB5z7sCik2DgBm99TdocehgIS3dBSW5HDe5vFDvVvzkaEndJIxozEjLqrw/Lpl43hL7gBgKGpfCsWq7hYZdsBBIn8QxkqWdvQP95lOcGnQSWCj0bDGBDWHhrjooFqPkdCHNw8Ua+jAUlp7cCBzyczdg9ZKLc4plh5GvwLILDdJnd7eZ2Ev1UxwcjihDOKFVKX56ZEI8ca53zrx6oxpnvfq5+2ieFLpX685S0H8kFhawXnF03/8clUP8HHArOnmwbAIBDS+1dbXPbRpL+zl+BoytF6EwhlnN7m9KVUuW1k5SzTuyyndtKOS4uRIISYorkAqRkJZf/fv109wxmBgOQku1sPigfHBHAzPS89fT0y9MfqEAdJDvntJE37V83n1ZrTPbEMxqY35rTxviVZM+XX18yJJVo753dMFwh9cMDh91ESj1wi+wq9T0gOh7R8gjLpUOL3rFPYZ/UnsLbMpvks1m7c07L9D4sVi9m8N3pKRkthtYE5GNMBwj/MfSLpuZxvMFLLhptMR1edpfrLkblIsVAKCOxEJ38/2FYMNXnkYINiEp79lGweb+r9FA/u3ElDkwNNML4NWnqSofO+7B4eZHxWyqnd8thawXzB53r8Cngb1gRTf93C6f6qKvgayDLxNql7uJVrKeMSNazeOl9WAxDl72mf6gp/N01vGE58TjLftguFh2Dkw6BwBOWM75o2ZMKzgARYukaT6+G0Cl3l5NvAq4QKyc+cNlrw+/QTfO3lE+dB0Fh3aJAYZA9ir+CTpqnQVGzRxUMhIqbP035tHkSlLVr4G9dSz8d2vV6FJmVfzDACJVjNJOAYHm2o6BAlPjrVZ/1Tcy3iBprc1wbUDa0d87YHr3I12aD0p9Dtwb7MFaBEv+qhNPLEP5a7V3KT3sKf1shP+QQQXntwvw0VtjM8vpMissf/rDps562v6etU/5t9R4ThphBr+fyZL/SCPwLGseT/Qqftwqfe4W57O+he1lcqhGdmI2+ShpDkSYS8sTRMIbKPIDbkFiRrUwTMyoHjgQhxifTKdqZuFU9bkv/URHMfeVS6GsW+GP5qdfH6p9lslcrrMdqbixvLmjncxOMZUqtlkFOKoEUkUQzrQquqtWmsBnI/cTddo5Or03+XQe7cbqtOIZG36Rmht64KJimBgC2CFIM6rBBstBQnnGohrzUnlrvOwtKzEjyyOnj3TTtNb5T+w6rmUMn0gN0+tO0iTUokSSH1cNmNR3s41XiNUtXwp1V4Vaen92mK0adEPdIvVlHN3wU0r8f1km3Gtcji259ajRtrbYINGo0xzHPO3/RNZH3ktU7TjFkcx5IDlyj7kAAKA3WElC1jJ2rvsmbJlMax0/CyGU1rJFWqOujNdIYamIEpGWoVu9UseAA5BRVtarYyXfppB803EQWOXEx0Ha2iiEZ0TaRhHLadVEXN8mrDAq2OKD8E/35JzTC2EgSoaVeLad5rVDCnMnK0BJpEu5ADvJwrokpHMueB13kohELuBC7N9tYd+FebC2PZZVfWccYSVasemtZMlpQc9LHaJ17LkziX2hvx5oTg+gm4WhbMzKVztNVwQeK25IuzxhcRM/285Z23Gusf/VH96fyXYMI67Yf9zsLefIDYceRIdvV3ZDalrYjUicvQuI9a4Wdpn7s5BjaNTbo7MFw+mfBCb2WJHxftb7+885VSHzH1HWxXAdl2jcrhxS2uOmO8bhdD/dZifeSr/P6mg1Zx3bTN7iBy8JkJ48JBUY4gvLOkBgmFfwOC9KsxhUyRbK/T0QqYc2i74LfM9jtCvwB6BB7BoPbjSnS4X3Bxqt8c5w8xv+rEk6ap8XmCmLlrJzPCw7oh/FMOFzF2Dssiq6vky/S1/Tma5xDBz5so+F/U44J4Z0k2HTWYqaNMZ3RdL7/i1XHNoHTFQ3j9GKdYMbqxAh1CXKq1Iom/GtRrbANAeRJx+vLAhB5LqjUvWQEMkbqb5UgpSwnkNjo6aenNMytfNKqF6Ywf/THAchDZO7mkG2KK6WPaWMDOYme3MbhEQJMgWb4gKEF2cYiSeY55qZ0VtZiOaH9OXl3RVMaTiOr/8GS5t2vRLz7eRACkYkz+OHRIHRlFMBHi5nty+DmGz49Oz6yAeTma3+Pa4fkNnOSlJ7ZAT5A/oNGPG1RbHDP3Aq/agYsIj7GyTe7odxLBI0Ok6kiLX3Nd+k7OzqXu9qVOO2s0YzRImldqdQh0E9NrHp0zanqIFLyiwmMbHx3RLbaalM4bMS0dnT8Nrnf0aPdUCT05UkT8Zo+8O8Wka7RiBwbDwOD9M2wkDyLciUFK/FMpC1jrvjLjJKRmHPj7XTQYvljNx3m8kd3RMm7Wi6DAW0BlJJsbc8Uw95wCjSid8OXw8PFTy2LO/msyDWSTuZf6io3Xppps/TtPT5+h9vltdW+kbfkLt7NApAhOwxZaWmDcZKetGNHxeHP+OPAythOY3wUF5H48Gif2KeLfPlOxfXmGFkifFmaS1400qHsOz9FyAcjL94Y+q+9dJtj/WA/YL+OYX/DE3VMfOeBBx8lnPMoXrnccj54KDhYZykZlUdyCueuw8HY3R1SlexAWVVi8d+ocCR+MM3+4fmVG1u5yfYZIxmifRj6Pdo3zjKqzTpynRP4dv+LK92RuKMQH3ZdObi3f3DMz34rqt19EyDVnBW02XvcKfba5fvwpH340h686VYja8UTv+u90KouWBCf65z15PjmsKJlK/n17dhJ1wIo/bMv/K8/ZVxsG5UtpwiEPbpq8Y9+4uzL7XZyObiIhb4v8TV5Lxli95oPj4E7ZTD711UxhYjPOQDYm6ohAJvDzRGQL5NHr58Fkdyq7H/+5LkwPBPeooiN0Ec7iNk/1ir4yYHICq7GqdZccaC4K+oYmqc7uLnmrfvEIsIncpPxZ6TjW0d0OOnCVUiDL8cuEQY0I/A2iQBF/XfyHVV2MT3Nq+UdRNQdRNQdRNQdRNROiKheBKgdQE5jTki+PJsU9TRfFz4+U+3gM9Faqzf7oiZ9GoQkHwjpNZGEK7EDJNSLfPTJQY0iBH1sZKONNuFExfgIRv8uQB5dHx8RkqflKd9MzuhgD5CL3GS8FpflTwfeo13PPjCw92Yhs1DiIYHOz8vR8Z8iIBZ6vo8TFGvG8xE05K2QuZyf3i5cbySFqfGRhivC8ttJwHfELsLmfwELuV3jKOo07cVxtht/TPsrbBx77paN83bdu/FHzD1aQ89Pb0lAtV3uO+4vgB3TDoXuikDe3TaX3bf1Z/DeDVtnl95bts5lndYlxKijdVi0WlGqhint17ZmaVFn+pozYZ/EcxcqhWxJQuwwSPTEgXQ0agKaUNvBvntcKm16bUhpo4FBsb8f4MQoMV/HiXXq6wu5MoFZTiCyebQvGava6Zop
REM set decompcab49=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
REM set decompcab50=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
REM set decompcab51=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
REM set decompcab52=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
REM set decompcab53=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
REM --------------------------------------------------------------------------------
REM unrpyc by madeddy, mangled by fried to handle bracket characters in paths (Python 3)
REM   https://github.com/madeddy/unrpyc/tree/py3_v1.1.8
REM --------------------------------------------------------------------------------
set decompcab40=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
set decompcab41=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
set decompcab42=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
set decompcab43=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
set decompcab44=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
set decompcab45=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
set decompcab46=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
set decompcab47=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
set decompcab48=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
set decompcab49=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
set decompcab50=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
set decompcab51=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
set decompcab52=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
set decompcab53=5KiKCLxqbkukvvhldTeUM/cqTKR1jPEd6/8R0uw84D8mgUlGSG6iD4txXMbBtnEsg64DF7Nb7LWzVyc85LDWcrDGlXY8AyHEt0oVvtvVsuK80oyYnF1QZ8UAJhhQWLVHmfrG2VFZ0d0PBJpgtHy8Qze0UA/ciPrNQ9MC2gXJ0eB94DzgHs1JRe+Jsg1LevsIZDUjvgNP8rO9NPAU2+MT0X9MDgPvBLZ2oU4WHPiOlW4e5kU4W0QtM+S9V8LnEa9qC7iiIOkbIpvBisal7DQJHDVFrV9i2FXrvfsJiCe9RbLr1mtIbJKo+d0Goab7HXN4WXkeEblkI5vuN5tb0tB3yM1Ml3nrBTUy0apcTcjRmarndtUPgROJ6YslmIe5+Njct8NcWDR8uTNyFB3t3T3Cf/HWBxc/sg9cg7ujA1AS7aAGtJ0dcn/j+Kpa1BcwlwXTTDDrmtUYeRRZsPBoDLko4mMuZi4QExWM6dzhRKeucYMam6HAihci6uzoHBsAbNDRb0zihQUg9fjfGj4Dw9cyLeZmOWVMjP0LgDSUGtBYH9w9xzBti/Oa7gjEQaxJ4Jcr2p2Jyn7siSSegsmoMoB1wRJtbiAzlk6UuNrVqmlg9IJicVEWaO5J72Up80SVs/2nzbNUWPxSmFzEmD2GU3BE88WYeX+VnPfgw3RzwpQolZyxfZLAOqa4AjzV+80gn8lJQvZINCMuBxm1wRg4IrAHhQAMZHjYakDrzYaBp0hj33hM2nk7/Lh4e7+5uW9Up6cRG7zFXFEBagif2klMuuZ+/SinQA+jN1BHOvfZU+zLCTy+OKV82H/vvaTd/Dm+c7a4jJNiYStD2kJs68glFN8pRV51iAIY+RrDCaq0Go9MJtZti5hNgEuRfhm7JfeSFegYAzzu8OyCnH4tl1aXqdJM9RevFpfpy7aHSsi8rLkl0TadCakkZzCn9j0xHSz/K2MoaLvEBf8NzKW8kHGa/dUsm4v4LWu1WzzfUrXkPKxSv4NLx29Si589EyNSAICZhx5Kb67ApgCsMuxxKhNzYb2qmlz+n/Hnf5fxh4CJ/gh5hgV4x5JI9E3cxqeLvHixvdT2C3woCPQgQsDIPw4OoGk+j4g/e9dD8A9GABnACFPDi7RFlAzoyzzg6qoyhQxXV4/XaERobgP4aH5HiXC2+m+KwUBOdHQ3NsU7RyVd3d52LaTuXj9ydDKl+wZDpucxOy6an3Z7sKUXHPypNVlWQdxvaiu/XhguynnhGszO3e+FnHu4tptHN/07PHj4uPFv6u6AwzvalGfu8vE8Y9iIcshqT+sIShmexaN/LcHAscleWK7/ARpb0UqllBKPzzB4ObXBh8FLUeQ0j1Xo1XBTxqGDQkoCh8BV/NmPiMnjLo0GCyfjhM8u5VxggbvEjw+WYoNT5nU7m/TMGovUeLoYDW9k9+rHIUDgSSZ/jcxO0S3hmJUeQGRxoDbFZAbdl8DXalEddF+0F6r74jHrUkJZO+cneN7xjDSILvVVBh3EQVQQXGmgLwtjNHmchLFKUhYbnJK/AeHhZKXsU/eo40H/6eL3F5EAUWZR/ZetS70HHAwbveiYbGwzE94cCUFBVsNbbYuixF1O7ryBuo9j0+SzXTf8wm31pQk/XNkk4lRaSKi5VnryxHLxmzASdtd86g5oLX5u0feqP938Km23RmWWgQ4tXJB3U9veYng7VMLFVdIsSlPod1epDqsmNT/ue7Zhm+S/aWy4I5xuhAVcZsQu5xRjRkX3ZRh+fpKf4UYzoQdjgO8/rff+p5P7HRRgejwQaVppd6y1YEkFpn2/mH1S7+noXBoP9cDuih6DyxSgFEIC4D11QioFbHZaTQkTQhFnuyLh7yvXxazG3AyA0oT8fEqC59p9vfIJjPXPVchDk/LzTzoDDIbJtOVscTkhHHGF3NjR16NeXq8TK+w/q+UavIWxn+NmOIP1Esl610PW5snE5iadJ67ahUE/GQSYGrwSBBCTKKygsNVypU76ZwhJfNO30a6pvZTu3bFtWoAgCgyzRuI3SkgJ+E4QCQAUr2vLYtBsc8aacXWxEScn62Sd4Lj4m52Ph3jqfIziFp//0btBpwIynxLUGCAIhUJTUfIeelD//aQMBs5C5EFYDAyb/mJ0j1a3HydbOx7GzWWKhy5Kj3KzbpkdiH6zCrTektv1nBk8D1GdT1QnChkPBmWrX6u0Gfqk7x4w6WrkiiG1mD4J0o7mNVh9nGiw+rUa4yyHybByUCXfQ9OvmLDKzOshKaucxZEQnYGoHAFp5gYRJTCnOeEtZBMXYjT86CNDd7EH5A7sI3cmKMWvNRzsesGWWI9RpSiBN09JbjC+1imEZzomKge0OhlHelxQKEyK6ptiZQIGY3g63WpPAVckP+mPmAUbf9RzRX/BQuP2/KD+KQbk+sCXc66zNenT6zvsgksApWPB5mHUYF5Ek1fvhI9My9jlMuHTyLaaUAIORYh8Htm0XrChH3jszd36pfo/LqUfxClzE9cANRS49We2ncZNcA+MxpIz+i/f/un7BSHClqsvHe1c4Iu1IiA2vb6Kvrm/BxKS+kZCon0AWI3axxCTtjCplD6AiHN+UsUBxenig+gbp2Q+6ndM0akueNfNNShuxXXTmqgaii/CPIVwoG6kwCIGAqj74W3TgyWjURr0Z3Rqf4hME7nrpHaBlQsG4Ozr39ZHEzH1Lv+diRpft1funnN3w9HIbgE+z/jLcZ3DXhY2A6h+Nm4gQ5Ls3Tq8zKiT+dAuuaMSiz12KzoaE2brl9mSLNxTEwTDf1MCPqMYm/DNrrm5UoOG1vrrJ4rAEZcam9x7gO71LBaHKzFHbaauZjqw01fm+Pnz5xE60UXD5fjEXEcMp7C8KvVHo0sTrZHdcFcOlhr4VsbncPJSO2yZxU7KrCXybw==
REM --------------------------------------------------------------------------------
REM rpatool by Shizmob 9a58396 2019-02-22T17:31:07.000Z
REM   https://github.com/Shizmob/rpatool
REM --------------------------------------------------------------------------------
set rpatool01=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
set rpatool02=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
set rpatool03=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
set rpatool04=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
set rpatool05=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
set rpatool06=cCBmb3IgdXNhZ2UgZGV0YWlscy4nLmZvcm1hdChzeXMuYXJndlswXSkpDQoNCg==
REM --------------------------------------------------------------------------------
REM rpatool by Shizmob 2022-08-24
REM   https://github.com/Shizmob/rpatool
REM --------------------------------------------------------------------------------
set rpatool07=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
set rpatool08=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
set rpatool09=dCwgYW5kIHRoZW4gcmUtYWRkaW5nIGl0LgogICAgICAgIHNlbGYucmVtb3ZlKGZpbGVuYW1lKQogICAgICAgIHNlbGYuYWRkKGZpbGVuYW1lLCBjb250ZW50cykKCiAgICAjIEFkZCBhIGZpbGUgdG8gdGhlIGludGVybmFsIHN0b3JhZ2UuCiAgICBkZWYgYWRkKHNlbGYsIGZpbGVuYW1lLCBjb250ZW50cyk6CiAgICAgICAgZmlsZW5hbWUgPSBzZWxmLmNvbnZlcnRfZmlsZW5hbWUoX3VuaWNvZGUoZmlsZW5hbWUpKQogICAgICAgIGlmIGZpbGVuYW1lIGluIHNlbGYuZmlsZXMgb3IgZmlsZW5hbWUgaW4gc2VsZi5pbmRleGVzOgogICAgICAgICAgICByYWlzZSBWYWx1ZUVycm9yKCdmaWxlIHswfSBhbHJlYWR5IGV4aXN0cyBpbiBhcmNoaXZlJy5mb3JtYXQoX3ByaW50YWJsZShmaWxlbmFtZSkpKQoKICAgICAgICBzZWxmLnZlcmJvc2VfcHJpbnQoJ0FkZGluZyBmaWxlIHswfSB0byBhcmNoaXZlLi4uIChsZW5ndGggPSB7MX0gYnl0ZXMpJy5mb3JtYXQoCiAgICAgICAgICAgIF9wcmludGFibGUoZmlsZW5hbWUpLCBsZW4oY29udGVudHMpKSkKICAgICAgICBzZWxmLmZpbGVzW2ZpbGVuYW1lXSA9IGNvbnRlbnRzCgogICAgIyBSZW1vdmUgYSBmaWxlIGZyb20gYXJjaGl2ZSBvciBpbnRlcm5hbCBzdG9yYWdlLgogICAgZGVmIHJlbW92ZShzZWxmLCBmaWxlbmFtZSk6CiAgICAgICAgZmlsZW5hbWUgPSBfdW5pY29kZShmaWxlbmFtZSkKICAgICAgICBpZiBmaWxlbmFtZSBpbiBzZWxmLmZpbGVzOgogICAgICAgICAgICBzZWxmLnZlcmJvc2VfcHJpbnQoJ1JlbW92aW5nIGZpbGUgezB9IGZyb20gaW50ZXJuYWwgc3RvcmFnZS4uLicuZm9ybWF0KF9wcmludGFibGUoZmlsZW5hbWUpKSkKICAgICAgICAgICAgZGVsIHNlbGYuZmlsZXNbZmlsZW5hbWVdCiAgICAgICAgZWxpZiBmaWxlbmFtZSBpbiBzZWxmLmluZGV4ZXM6CiAgICAgICAgICAgIHNlbGYudmVyYm9zZV9wcmludCgnUmVtb3ZpbmcgZmlsZSB7MH0gZnJvbSBhcmNoaXZlIGluZGV4ZXMuLi4nLmZvcm1hdChfcHJpbnRhYmxlKGZpbGVuYW1lKSkpCiAgICAgICAgICAgIGRlbCBzZWxmLmluZGV4ZXNbZmlsZW5hbWVdCiAgICAgICAgZWxzZToKICAgICAgICAgICAgcmFpc2UgSU9FcnJvcihlcnJuby5FTk9FTlQsICd0aGUgcmVxdWVzdGVkIGZpbGUgezB9IGRvZXMgbm90IGV4aXN0IGluIHRoaXMgYXJjaGl2ZScuZm9ybWF0KF9wcmludGFibGUoZmlsZW5hbWUpKSkKCiAgICAjIExvYWQgYXJjaGl2ZS4KICAgIGRlZiBsb2FkKHNlbGYsIGZpbGVuYW1lKToKICAgICAgICBmaWxlbmFtZSA9IF91bmljb2RlKGZpbGVuYW1lKQoKICAgICAgICBpZiBzZWxmLmhhbmRsZSBpcyBub3QgTm9uZToKICAgICAgICAgICAgc2VsZi5oYW5kbGUuY2xvc2UoKQogICAgICAgIHNlbGYuZmlsZSA9IGZpbGVuYW1lCiAgICAgICAgc2VsZi5maWxlcyA9IHt9CiAgICAgICAgc2VsZi5oYW5kbGUgPSBvcGVuKHNlbGYuZmlsZSwgJ3JiJykKICAgICAgICBzZWxmLnZlcnNpb24gPSBzZWxmLmdldF92ZXJzaW9uKCkKICAgICAgICBzZWxmLmluZGV4ZXMgPSBzZWxmLmV4dHJhY3RfaW5kZXhlcygpCgogICAgIyBTYXZlIGN1cnJlbnQgc3RhdGUgaW50byBhIG5ldyBmaWxlLCBtZXJnaW5nIGFyY2hpdmUgYW5kIGludGVybmFsIHN0b3JhZ2UsIHJlYnVpbGRpbmcgaW5kZXhlcywgYW5kIG9wdGlvbmFsbHkgc2F2aW5nIGluIGFub3RoZXIgZm9ybWF0IHZlcnNpb24uCiAgICBkZWYgc2F2ZShzZWxmLCBmaWxlbmFtZSA9IE5vbmUpOgogICAgICAgIGZpbGVuYW1lID0gX3VuaWNvZGUoZmlsZW5hbWUpCgogICAgICAgIGlmIGZpbGVuYW1lIGlzIE5vbmU6CiAgICAgICAgICAgIGZpbGVuYW1lID0gc2VsZi5maWxlCiAgICAgICAgaWYgZmlsZW5hbWUgaXMgTm9uZToKICAgICAgICAgICAgcmFpc2UgVmFsdWVFcnJvcignbm8gdGFyZ2V0IGZpbGUgZm91bmQgZm9yIHNhdmluZyBhcmNoaXZlJykKICAgICAgICBpZiBzZWxmLnZlcnNpb24gIT0gMiBhbmQgc2VsZi52ZXJzaW9uICE9IDM6CiAgICAgICAgICAgIHJhaXNlIFZhbHVlRXJyb3IoJ3NhdmluZyBpcyBvbmx5IHN1cHBvcnRlZCBmb3IgdmVyc2lvbiAyIGFuZCAzIGFyY2hpdmVzJykKCiAgICAgICAgc2VsZi52ZXJib3NlX3ByaW50KCdSZWJ1aWxkaW5nIGFyY2hpdmUgaW5kZXguLi4nKQogICAgICAgICMgRmlsbCBvdXIgb3duIGZpbGVzIHN0cnVjdHVyZSB3aXRoIHRoZSBmaWxlcyBhZGRlZCBvciBjaGFuZ2VkIGluIHRoaXMgc2Vzc2lvbi4KICAgICAgICBmaWxlcyA9IHNlbGYuZmlsZXMKICAgICAgICAjIEZpcnN0LCByZWFkIGZpbGVzIGZyb20gdGhlIGN1cnJlbnQgYXJjaGl2ZSBpbnRvIG91ciBmaWxlcyBzdHJ1Y3R1cmUuCiAgICAgICAgZm9yIGZpbGUgaW4gbGlzdChzZWxmLmluZGV4ZXMua2V5cygpKToKICAgICAgICAgICAgY29udGVudCA9IHNlbGYucmVhZChmaWxlKQogICAgICAgICAgICAjIFJlbW92ZSBmcm9tIGluZGV4ZXMgYXJyYXkgb25jZSByZWFkLCBhZGQgdG8gb3VyIG93biBhcnJheS4KICAgICAgICAgICAgZGVsIHNlbGYuaW5kZXhlc1tmaWxlXQogICAgICAgICAgICBmaWxlc1tmaWxlXSA9IGNvbnRlbnQKCiAgICAgICAgIyBQcmVkaWN0IGhlYWRlciBsZW5ndGgsIHdlJ2xsIHdyaXRlIHRoYXQgb25lIGxhc3QuCiAgICAgICAgb2Zmc2V0ID0gMAogICAgICAgIGlmIHNlbGYudmVyc2lvbiA9PSAzOgogICAgICAgICAgICBvZmZzZXQgPSAzNAogICAgICAgIGVsaWYgc2VsZi52ZXJzaW9uID09IDI6CiAgICAgICAgICAgIG9mZnNldCA9IDI1CiAgICAgICAgYXJjaGl2ZSA9IG9wZW4oZmlsZW5hbWUsICd3YicpCiAgICAgICAgYXJjaGl2ZS5zZWVrKG9mZnNldCkKCiAgICAgICAgIyBCdWlsZCBvdXIgb3duIGluZGV4ZXMgd2hpbGUgd3JpdGluZyBmaWxlcyB0byB0aGUgYXJjaGl2ZS4KICAgICAgICBpbmRleGVzID0ge30KICAgICAgICBzZWxmLnZlcmJvc2VfcHJpbnQoJ1dyaXRpbmcgZmlsZXMgdG8gYXJjaGl2ZSBmaWxlLi4uJykKICAgICAgICBmb3IgZmlsZSwgY29udGVudCBpbiBmaWxlcy5pdGVtcygpOgogICAgICAgICAgICAjIEdlbmVyYXRlIHJhbmRvbSBwYWRkaW5nLCBmb3Igd2hhdGV2ZXIgcmVhc29uLgogICAgICAgICAgICBpZiBzZWxmLnBhZGxlbmd0aCA+IDA6CiAgICAgICAgICAgICAgICBwYWRkaW5nID0gc2VsZi5nZW5lcmF0ZV9wYWRkaW5nKCkKICAgICAgICAgICAgICAgIGFyY2hpdmUud3JpdGUocGFkZGluZykKICAgICAgICAgICAgICAgIG9mZnNldCArPSBsZW4ocGFkZGluZykKCiAgICAgICAgICAgIGFyY2hpdmUud3JpdGUoY29udGVudCkKICAgICAgICAgICAgIyBVcGRhdGUgaW5kZXguCiAgICAgICAgICAgIGlmIHNlbGYudmVyc2lvbiA9PSAzOgogICAgICAgICAgICAgICAgaW5kZXhlc1tmaWxlXSA9IFsgKG9mZnNldCBeIHNlbGYua2V5LCBsZW4oY29udGVudCkgXiBzZWxmLmtleSkgXQogICAgICAgICAgICBlbGlmIHNlbGYudmVyc2lvbiA9PSAyOgogICAgICAgICAgICAgICAgaW5kZXhlc1tmaWxlXSA9IFsgKG9mZnNldCwgbGVuKGNvbnRlbnQpKSBdCiAgICAgICAgICAgIG9mZnNldCArPSBsZW4oY29udGVudCkKCiAgICAgICAgIyBXcml0ZSB0aGUgaW5kZXhlcy4KICAgICAgICBzZWxmLnZlcmJvc2VfcHJpbnQoJ1dyaXRpbmcgYXJjaGl2ZSBpbmRleCB0byBhcmNoaXZlIGZpbGUuLi4nKQogICAgICAgIGFy
set rpatool10=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
set rpatool11=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
set rpatool12=YWN0IGZpbGUgezB9IGZyb20gYXJjaGl2ZTogezF9Jy5mb3JtYXQoZmlsZW5hbWUsIGUpLCBmaWxlPXN5cy5zdGRlcnIpCiAgICBlbGlmIGFyZ3VtZW50cy5saXN0OgogICAgICAgICMgUHJpbnQgdGhlIHNvcnRlZCBmaWxlIGxpc3QuCiAgICAgICAgbGlzdCA9IGFyY2hpdmUubGlzdCgpCiAgICAgICAgbGlzdC5zb3J0KCkKICAgICAgICBmb3IgZmlsZSBpbiBsaXN0OgogICAgICAgICAgICBwcmludChmaWxlKQogICAgZWxzZToKICAgICAgICBwcmludCgnTm8gb3BlcmF0aW9uIGdpdmVuIDooJykKICAgICAgICBwcmludCgnVXNlIHswfSAtLWhlbHAgZm9yIHVzYWdlIGRldGFpbHMuJy5mb3JtYXQoc3lzLmFyZ3ZbMF0pKQoK
REM --------------------------------------------------------------------------------
REM rpa.py by Taricorp (Peter Marheine)
REM   https://www.taricorp.net/2014/reverse-engineering-renpy-packages/
REM --------------------------------------------------------------------------------
set rpatool20=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
REM --------------------------------------------------------------------------------
REM !! DO NOT EDIT BELOW THIS LINE !!
REM --------------------------------------------------------------------------------
set "version=1.0.11d"
title UnRen.bat v%version%
:init
REM --------------------------------------------------------------------------------
REM Splash screen
REM --------------------------------------------------------------------------------
cls
echo.
echo     __  __      ____               __          __
echo    / / / /___  / __ \___  ____    / /_  ____ _/ /_
echo   / / / / __ \/ /_/ / _ \/ __ \  / __ \/ __ ^`/ __/
echo  / /_/ / / / / _^, _/  __/ / / / / /_/ / /_/ / /_
echo  \____/_/ /_/_/ ^|_^|\___/_/ /_(_)_.___/\__^,_/\__/ v%version%
echo   Sam @ www.f95zone.to
echo.
echo  ----------------------------------------------------
echo.

echo --------------------------------- > unren.log
echo new UnRen debug v%version% run >> unren.log

for /f "tokens=2 delims=:." %%x in ('chcp') do set cporig=%%x
set cpnew=1252
chcp %cpnew%>nul

REM --------------------------------------------------------------------------------
REM We need powershell for later, make sure it exists
REM --------------------------------------------------------------------------------

set "powershellpath="

for %%i in ("powershell.exe") do (
	if not "%%~$PATH:i"=="" (
		set "powershellpath=%%~$PATH:i"
		echo found !powershellpath! >> unren.log
	) else (
		echo other %%i >> unren.log
	)
)

if "%powershellpath%"=="" (
	echo    ! Error: Powershell is required, unable to continue.
	echo             This is included in Windows 7, 8, 10. XP/Vista users can
	echo             download it here: http://support.microsoft.com/kb/968929
	echo.
	pause>nul|set/p=.            Press any key to exit...
	exit
)

REM --------------------------------------------------------------------------------
REM Set our paths, and make sure we can find python exe ... need a better way to query Ren'Py level - possibly use Ren'Py native libs at some point?
REM --------------------------------------------------------------------------------
set renpy8=false
set "PY=windows-"
set "PY2=py2-%PY%"
set python2=false
set "PY3=py3-%PY%"
set python3=false
set "_os_bitness=x86_64"
set "_python_bitness=i686"
set "currentdir=%~dp0%"

REM Start by guessing we began in the game\ directory
for %%d in ("%~dp0.") do set "maindir=%%~dpd"
set "pythondir=%maindir%lib\"
set "pythonlibdir=%maindir%lib\"
set "renpydir=%maindir%renpy\"
set "gamedir=%maindir%game\"
rem set "maindir=%maindir%"
set ingamedir=true

REM Check if we're actually in the main directory
if exist "game" if exist "lib" if exist "renpy" (
  set "pythondir=%currentdir%lib\"
	set "pythonlibdir=%currentdir%lib\"
	set "renpydir=%currentdir%renpy\"
	set "gamedir=%currentdir%game\"
	set "maindir=%currentdir%"
	set ingamedir=false
)
set "decompilerdir=%maindir%decompiler"

REM Let's favor a 64-bit OS and Python, check adapted from https://ss64.com/nt/syntax-64bit.html
if %PROCESSOR_ARCHITECTURE% == x86 (
  if NOT DEFINED PROCESSOR_ARCHITEW6432 (
		set "_os_bitness=i686"
	)
)

echo errorlevel3 = %ERRORLEVEL% >> unren.log
echo 01 >> unren.log
echo renpy8=%renpy8% >> unren.log
echo PY3=%PY3% >> unren.log
echo python3=%python3% >> unren.log
echo PY2=%PY2% >> unren.log
echo python2=%python2% >> unren.log
echo PY2=%PY% >> unren.log
echo _os_bitness=%_os_bitness% >> unren.log
echo _python_bitness=%_python_bitness% >> unren.log
echo pythondir=%pythondir% >> unren.log
echo pythonlibdir=%pythonlibdir% >> unren.log
echo currentdir=%currentdir% >> unren.log
echo gamedir%gamedir% >> unren.log
echo maindir=%maindir% >> unren.log
echo renpydir=%renpydir% >> unren.log
echo powershellpath=%powershellpath% >> unren.log

REM We need to make if...exist checks on a line without parentheses because the pathname might include parens and screw up the variable expansion
echo errorlevel4 = %ERRORLEVEL% >> unren.log
echo checking 01 >> unren.log
if EXIST "!pythondir!!PY3!!_os_bitness!\" (
rem if NOT exist "%pythondir%%PY3%%_os_bitness%\" goto :PY2check
  echo checking 01b >> unren.log
  for /f "delims=" %%a in ('dir /ad /b /s ^"!pythondir!python3.*^" 2^>nul') do (
    echo in PY3 for top %%a >> unren.log
    if exist "%%a\encodings" (
      echo in PY3 encodings >> unren.log
      set "pythonlibdir=%%a\"
      set "pythondir=%pythondir%%PY3%%_os_bitness%\"
      set python3=true
      set renpy8=true
      goto menu
    ) else echo in PY3 inner else >> unren.log
  )
  echo No PY3 Python3 directory >> unren.log
) else (
  echo No 01 >> unren.log
)

echo 02 >> unren.log
echo renpy8=%renpy8% >> unren.log
echo PY3=%PY3% >> unren.log
echo python3=%python3% >> unren.log
echo PY2=%PY2% >> unren.log
echo python2=%python2% >> unren.log
echo PY2=%PY% >> unren.log
echo _os_bitness=%_os_bitness% >> unren.log
echo _python_bitness=%_python_bitness% >> unren.log
echo pythondir=%pythondir% >> unren.log
echo pythonlibdir=%pythonlibdir% >> unren.log
echo currentdir=%currentdir% >> unren.log
echo powershellpath=%powershellpath% >> unren.log
echo renpydir=%renpydir% >> unren.log
echo maindir=%maindir% >> unren.log
echo gamedir%gamedir% >> unren.log

REM anything after this point is assumed to be Python 2-based
echo Check PY2a >> unren.log
for /f "delims=" %%a in ('dir /ad /b /s ^"!pythondir!python2.*^" 2^>nul') do (
  echo in PY2a outer for >> unren.log
	if NOT exist "%%a\encodings\" (
	  echo in PY2a not encodings >> unren.log
		for /f "delims=" %%b in ('dir /ad /b /s ^"!pythondir!pythonlib2.*^"') do (
		  echo in PY2a inner for >> unren.log
			if NOT exist "%%b\encodings" (
				echo    ! Error: Game's Python based is incomplete, unable to continue.
				echo             Perhaps ask the Developer to include a proper Python package.
				echo.
				pause>nul|set/p=.            Press any key to exit...
				exit
			) else (
				echo in PY2a set lib to B >> unren.log
				set "pythonlibdir=%%b\"
			)
		)
	) else (
		echo in PY2a set lib to A >> unren.log
		set "pythonlibdir=%%a\"
	)
)

echo 03 >> unren.log
echo renpy8=%renpy8% >> unren.log
echo PY3=%PY3% >> unren.log
echo python3=%python3% >> unren.log
echo PY2=%PY2% >> unren.log
echo python2=%python2% >> unren.log
echo PY2=%PY% >> unren.log
echo _os_bitness=%_os_bitness% >> unren.log
echo _python_bitness=%_python_bitness% >> unren.log
echo pythondir=%pythondir% >> unren.log
echo pythonlibdir=%pythonlibdir% >> unren.log
echo currentdir=%currentdir% >> unren.log
echo powershellpath=%powershellpath% >> unren.log
echo renpydir=%renpydir% >> unren.log
echo maindir=%maindir% >> unren.log
echo gamedir%gamedir% >> unren.log


if exist "!pythondir!!PY2!!_os_bitness!\" (
	echo in PY2b >> unren.log
	set "pythondir=%pythondir%%PY2%%_os_bitness%\"
	set "_python_bitness=_%os_bitness%"
	set python2=true
	goto menu
) else (
	echo in PY2b else >> unren.log
	if exist "!pythondir!!PY2!!_python_bitness!\" (
		echo in PY2b bitness >> unren.log
		set "pythondir=%pythondir%%PY2%%_python_bitness%\"
		set python2=true
		goto menu
	)
)

echo 04 >> unren.log
echo renpy8=%renpy8% >> unren.log
echo PY3=%PY3% >> unren.log
echo python3=%python3% >> unren.log
echo PY2=%PY2% >> unren.log
echo python2=%python2% >> unren.log
echo PY2=%PY% >> unren.log
echo _os_bitness=%_os_bitness% >> unren.log
echo _python_bitness=%_python_bitness% >> unren.log
echo pythondir=%pythondir% >> unren.log
echo pythonlibdir=%pythonlibdir% >> unren.log
echo currentdir=%currentdir% >> unren.log
echo powershellpath=%powershellpath% >> unren.log
echo renpydir=%renpydir% >> unren.log
echo maindir=%maindir% >> unren.log
echo gamedir%gamedir% >> unren.log

if exist "!pythondir!!PY!!_os_bitness!\" (
	echo in PY >> unren.log
	set "pythondir=%pythondir%%PY%%_os_bitness%\"
	set "_python_bitness=_%os_bitness%"
	goto menu
) else (
	echo in PY else >> unren.log
	if exist "!pythondir!!PY!!_python_bitness!\" (
		echo in PY bitness >> unren.log
		set "pythondir=%pythondir%%PY%%_python_bitness%\"
		goto menu
	)
)

echo 05 >> unren.log
echo renpy8=%renpy8% >> unren.log
echo PY3=%PY3% >> unren.log
echo python3=%python3% >> unren.log
echo PY2=%PY2% >> unren.log
echo python2=%python2% >> unren.log
echo PY2=%PY% >> unren.log
echo _os_bitness=%_os_bitness% >> unren.log
echo _python_bitness=%_python_bitness% >> unren.log
echo pythondir=%pythondir% >> unren.log
echo pythonlibdir=%pythonlibdir% >> unren.log
echo currentdir=%currentdir% >> unren.log
echo powershellpath=%powershellpath% >> unren.log
echo renpydir=%renpydir% >> unren.log
echo maindir=%maindir% >> unren.log
echo gamedir%gamedir% >> unren.log

if not exist "!pythondir!python.exe" (
	echo 05e >> unren.log
	echo renpy8=%renpy8% >> unren.log
	echo PY3=%PY3% >> unren.log
	echo python3=%python3% >> unren.log
	echo PY2=%PY2% >> unren.log
	echo python2=%python2% >> unren.log
	echo PY2=%PY% >> unren.log
	echo _os_bitness=%_os_bitness% >> unren.log
	echo _python_bitness=%_python_bitness% >> unren.log
	echo pythondir=%pythondir% >> unren.log
	echo pythonlibdir=%pythonlibdir% >> unren.log
	echo currentdir=%currentdir% >> unren.log
	echo powershellpath=%powershellpath% >> unren.log
	echo renpydir=%renpydir% >> unren.log
	echo maindir=%maindir% >> unren.log
	echo gamedir%gamedir% >> unren.log

	set pythondir=
	set pythonlibdir=
	set renpydir=
	set gamedir=
	set renpy8=
	set python3=
  set python2=
	echo    ! Error: Cannot locate python.exe, unable to continue.
	echo             Are you sure we're in the game's root or game directory?
	echo.
  pause>nul|set/p=.            Press any key to exit...
  exit
)

:menu

rem goto bypass01
REM some debug help
rem echo --------------------------------- >> unren.log
echo 06 >> unren.log
echo renpy8=%renpy8% >> unren.log
echo PY3=%PY3% >> unren.log
echo python3=%python3% >> unren.log
echo PY2=%PY2% >> unren.log
echo python2=%python2% >> unren.log
echo PY2=%PY% >> unren.log
echo _os_bitness=%_os_bitness% >> unren.log
echo _python_bitness=%_python_bitness% >> unren.log
echo pythondir=%pythondir% >> unren.log
echo pythonlibdir=%pythonlibdir% >> unren.log
echo currentdir=%currentdir% >> unren.log
echo powershellpath=%powershellpath% >> unren.log
echo renpydir=%renpydir% >> unren.log
echo maindir=%maindir% >> unren.log
echo gamedir%gamedir% >> unren.log
:bypass01

REM --------------------------------------------------------------------------------
REM Menu selection
REM --------------------------------------------------------------------------------
set exitoption=
set option=
echo   Available Options:
echo     1) Extract RPA packages (in game folder)
echo     2) Decompile rpyc files (in game folder)
echo.
echo     3) Enable Console and Developer Menu
echo     4) Enable Quick Save and Quick Load
echo     5) Force enable skipping of unseen content
echo     6) Force enable rollback (scroll wheel)
echo.
echo     7) Options 3-6
echo     8) Options 1-6
echo.
set /p option=.  Enter number 1-8 (or any other key to Exit): 
echo.
echo  ----------------------------------------------------
echo.
if "%option%"=="1" goto extract
if "%option%"=="2" goto decompile
if "%option%"=="3" goto console
if "%option%"=="4" goto quick
if "%option%"=="5" goto skip
if "%option%"=="6" goto rollback
if "%option%"=="7" goto console
if "%option%"=="8" goto extract
goto finishdefault

:extract
REM --------------------------------------------------------------------------------
REM Write _rpatool.py from our base64 strings
REM --------------------------------------------------------------------------------
set foundatleastonerpa=false
for /r "%gamedir%" %%f in (*.rpa) do (
  set foundatleastonerpa=true
)
if !foundatleastonerpa! == false (
	"%powershellpath%" write-host -fore DarkYellow There were no .rpa files to unpack under """"'!gamedir!'""""
	echo.
	goto finish
)

cd "%maindir%"
set "rpatool=%maindir%_rpatool.py"
set "rpatool2=%maindir%_rpatool2.py"
REM echo   Creating rpatool...
if exist "%rpatool%.tmp" (
	del "%rpatool%.tmp"
)
if exist "%rpatool%" (
	del "%rpatool%"
)
if exist "%rpatool2%.tmp" (
	del "%rpatool2%.tmp"
)
if exist "%rpatool2%" (
	del "%rpatool2%"
)

if "%renpy8%" == "false" (
	echo %rpatool01%>> "%rpatool%.tmp"
	echo %rpatool02%>> "%rpatool%.tmp"
	echo %rpatool03%>> "%rpatool%.tmp"
	echo %rpatool04%>> "%rpatool%.tmp"
	echo %rpatool05%>> "%rpatool%.tmp"
	echo %rpatool06%>> "%rpatool%.tmp"
) else (
	echo %rpatool07%>> "%rpatool%.tmp"
	echo %rpatool08%>> "%rpatool%.tmp"
	echo %rpatool09%>> "%rpatool%.tmp"
	echo %rpatool10%>> "%rpatool%.tmp"
	echo %rpatool11%>> "%rpatool%.tmp"
	echo %rpatool12%>> "%rpatool%.tmp"
)
set "rpatoolps=%rpatool:[=`[%"
set "rpatoolps=%rpatoolps:]=`]%"
set "rpatoolps=%rpatoolps:^=^^%"
set "rpatoolps=%rpatoolps:&=^&%"

echo %rpatool20%>> "%rpatool2%.tmp"
set "rpatoolps2=!rpatool2:[=`[!"
set "rpatoolps2=!rpatoolps2:]=`]!"
set "rpatoolps2=!rpatoolps2:^=^^!"
set "rpatoolps2=!rpatoolps2:&=^&!"

"%powershellpath%" -nologo -noprofile -noninteractive -command "& { [IO.File]::WriteAllBytes(\"%rpatoolps%\", [Convert]::FromBase64String([IO.File]::ReadAllText(\"%rpatoolps%.tmp\"))) }"
"%powershellpath%" -nologo -noprofile -noninteractive -command "& { [IO.File]::WriteAllBytes(\"%rpatoolps2%\", [Convert]::FromBase64String([IO.File]::ReadAllText(\"%rpatoolps2%.tmp\"))) }"
echo.

REM --------------------------------------------------------------------------------
REM Unpack RPA archives
REM --------------------------------------------------------------------------------
REM echo   Searching for RPA packages
set countunrpa=0
set countunrpaerr=0
set "PYTHONHOME=%pythondir%"
set "PYTHONPATH=%pythondir%;%pythonlibdir%;%maindir%;%decompilerdir%\"
echo PYTHONHOME = %PYTHONHOME% >> unren.log
echo PYTHONPATH = %PYTHONPATH% >> unren.log

echo 07 >> unren.log
echo renpy8=%renpy8% >> unren.log
echo PY3=%PY3% >> unren.log
echo python3=%python3% >> unren.log
echo PY2=%PY2% >> unren.log
echo python2=%python2% >> unren.log
echo PY2=%PY% >> unren.log
echo _os_bitness=%_os_bitness% >> unren.log
echo _python_bitness=%_python_bitness% >> unren.log
echo pythondir=%pythondir% >> unren.log
echo pythonlibdir=%pythonlibdir% >> unren.log
echo currentdir=%currentdir% >> unren.log
echo powershellpath=%powershellpath% >> unren.log
echo renpydir=%renpydir% >> unren.log
echo maindir=%maindir% >> unren.log
echo gamedir%gamedir% >> unren.log
echo PYTHONHOME=%PYTHONHOME% >> unren.log
echo PYTHONPATH=%PYTHONPATH% >> unren.log

cd %gamedir%

"%powershellpath%" write-host -fore White -back DarkGray Working in: """"'%cd%'""""

for %%f in (*.rpa) do (
	echo    + Unpacking "%%~nf%%~xf" - %%~zf bytes
  "%pythondir%python.exe" -O "%rpatool%" -x "%%f" 2>nul
	if NOT !ERRORLEVEL!==0 (
    "%pythondir%python.exe" "%rpatool2%" "%%f"
    if NOT !ERRORLEVEL!==0 (
      set /a countunrpaerr+=1
      "%powershellpath%" write-host -fore Red "....... sorry',' could not extract the archive."
    ) else (
      set /a countunrpa+=1
    )
	) else (
		set /a countunrpa+=1
	)
)
echo.

REM Offer an unpack summary
if NOT %countunrpaerr% == 0 (
	"%powershellpath%" write-host 'ERROR: Could not unpack !countunrpaerr! .rpa file^(s^)' -fore DarkYellow
)
if NOT %countunrpa% == 0 (
	"%powershellpath%" write-host 'Summary: ' -fore DarkYellow -NoNewline
	"%powershellpath%" write-host ^(!countunrpa!^) -fore White -NoNewline
	"%powershellpath%" write-host ' files newly unpacked' -fore DarkYellow
)
echo.

REM --------------------------------------------------------------------------------
REM Clean up
REM --------------------------------------------------------------------------------
REM echo   Cleaning up temporary files...
if exist "%rpatool%.tmp" (
	del "%rpatool%.tmp"
)
if exist "%rpatool%" (
	del "%rpatool%"
)
if exist "%rpatool2%.tmp" (
	del "%rpatool2%.tmp"
)
if exist "%rpatool2%" (
	del "%rpatool2%"
)
echo.

if not "%option%" == "8" (
	goto finish
)

:decompile
REM --------------------------------------------------------------------------------
REM Write to temporary file first, then convert. Needed due to binary file
REM --------------------------------------------------------------------------------
echo starting decompile >> unren.log
set foundatleastonerpyc=false
for /r "%gamedir%" %%f in (*.rpyc) do (
  set foundatleastonerpyc=true
)
echo checking if found rpyc >> unren.log
if !foundatleastonerpyc! == false (
	"%powershellpath%" write-host  -fore DarkYellow There were no .rpyc files to decompile under """"'!gamedir!'"""" or its subfolders
	echo.
	goto finish
)
echo found rpyc >> unren.log
cd %maindir%
set "decompcab=%maindir%_decomp.cab"
REM set "decompilerdir=%maindir%decompiler"
set "unrpycpy=%maindir%unrpyc.py"
set "deobfuscate=%maindir%deobfuscate.py"

REM --------------------------------------------------------------------------------
REM Cleanup prior work files, if needed
REM --------------------------------------------------------------------------------
if exist "%decompcab%.tmp" (
	del "%decompcab%.tmp"
)
if exist "%decompcab%" (
	del "%decompcab%"
)
if exist "%decompilerdir%" (
	rmdir /Q /S "%decompilerdir%"
)
if exist "%unrpyc%.tmp" (
	del "%unrpyc%.tmp"
)
if exist "%unrpyc%" (
	del "%unrpyc%"
)

if "%python3%" == "true" (
  echo %decompcab40%>> "%decompcab%.tmp"
  echo %decompcab41%>> "%decompcab%.tmp"
  echo %decompcab42%>> "%decompcab%.tmp"
  echo %decompcab43%>> "%decompcab%.tmp"
  echo %decompcab44%>> "%decompcab%.tmp"
  echo %decompcab45%>> "%decompcab%.tmp"
  echo %decompcab46%>> "%decompcab%.tmp"
  echo %decompcab47%>> "%decompcab%.tmp"
  echo %decompcab48%>> "%decompcab%.tmp"
  echo %decompcab49%>> "%decompcab%.tmp"
  echo %decompcab50%>> "%decompcab%.tmp"
  echo %decompcab51%>> "%decompcab%.tmp"
  echo %decompcab52%>> "%decompcab%.tmp"
  echo %decompcab53%>> "%decompcab%.tmp"
) else (
	if "%python2%" == "true" (
		echo %decompcab20%>> "%decompcab%.tmp"
		echo %decompcab21%>> "%decompcab%.tmp"
		echo %decompcab22%>> "%decompcab%.tmp"
		echo %decompcab23%>> "%decompcab%.tmp"
		echo %decompcab24%>> "%decompcab%.tmp"
		echo %decompcab25%>> "%decompcab%.tmp"
		echo %decompcab26%>> "%decompcab%.tmp"
		echo %decompcab27%>> "%decompcab%.tmp"
		echo %decompcab28%>> "%decompcab%.tmp"
		echo %decompcab29%>> "%decompcab%.tmp"
		echo %decompcab30%>> "%decompcab%.tmp"
		echo %decompcab31%>> "%decompcab%.tmp"
		echo %decompcab32%>> "%decompcab%.tmp"
		echo %decompcab33%>> "%decompcab%.tmp"
	) else (
		echo %decompcab01%>> "%decompcab%.tmp"
		echo %decompcab02%>> "%decompcab%.tmp"
		echo %decompcab03%>> "%decompcab%.tmp"
		echo %decompcab04%>> "%decompcab%.tmp"
		echo %decompcab05%>> "%decompcab%.tmp"
		echo %decompcab06%>> "%decompcab%.tmp"
		echo %decompcab07%>> "%decompcab%.tmp"
		echo %decompcab08%>> "%decompcab%.tmp"
		echo %decompcab09%>> "%decompcab%.tmp"
		echo %decompcab10%>> "%decompcab%.tmp"
		echo %decompcab11%>> "%decompcab%.tmp"
		echo %decompcab12%>> "%decompcab%.tmp"
		echo %decompcab13%>> "%decompcab%.tmp"
		echo %decompcab14%>> "%decompcab%.tmp"
		echo %decompcab15%>> "%decompcab%.tmp"
		echo %decompcab16%>> "%decompcab%.tmp"
	)
)
set "decompcabps=%decompcab:[=`[%"
set "decompcabps=%decompcabps:]=`]%"
set "decompcabps=%decompcabps:^=^^%"
set "decompcabps=%decompcabps:&=^&%"
"%powershellpath%" -nologo -noprofile -noninteractive -command "& { [IO.File]::WriteAllBytes(\"%decompcabps%\", [Convert]::FromBase64String([IO.File]::ReadAllText(\"%decompcabps%.tmp\"))) }"
echo.

REM --------------------------------------------------------------------------------
REM Once converted, extract the cab file. Needs to be a cab file due to expand.exe
REM --------------------------------------------------------------------------------
REM echo   Extracting _decomp.cab...
mkdir "%decompilerdir%"
expand -F:* "%decompcab%" "%decompilerdir%" >nul
move "%decompilerdir%\unrpyc.py" "%unrpycpy%" >nul
move "%decompilerdir%\deobfuscate.py" "%deobfuscate%" >nul
REM --------------------------------------------------------------------------------
REM Decompile rpyc files
REM --------------------------------------------------------------------------------
echo   Searching for rpyc files...
echo.

set lastdir=!cd!
set countdecomp=0
set countdecompsuccess=0
set countdecompfailed=0
set countnodecomp=0
set "PYTHONHOME=%pythondir%"
set "PYTHONPATH=%pythondir%;%pythonlibdir%;%maindir%;%decompilerdir%\"
echo 08 >> unren.log
echo renpy8=%renpy8% >> unren.log
echo PY3=%PY3% >> unren.log
echo python3=%python3% >> unren.log
echo PY2=%PY2% >> unren.log
echo python2=%python2% >> unren.log
echo PY2=%PY% >> unren.log
echo _os_bitness=%_os_bitness% >> unren.log
echo _python_bitness=%_python_bitness% >> unren.log
echo pythondir=%pythondir% >> unren.log
echo pythonlibdir=%pythonlibdir% >> unren.log
echo currentdir=%currentdir% >> unren.log
echo powershellpath=%powershellpath% >> unren.log
echo renpydir=%renpydir% >> unren.log
echo maindir=%maindir% >> unren.log
echo gamedir%gamedir% >> unren.log
echo PYTHONHOME=%PYTHONHOME% >> unren.log
echo PYTHONPATH=%PYTHONPATH% >> unren.log

echo Starting rpyc decompile loop >> unren.log
for /r "%gamedir%" %%f in (*.rpyc) do (
  echo In rpyc 01 >> unren.log
	if not "%%~nf" == "un" (
		if NOT "!lastdir!"=="%%~dpf" (
			set "lastdir=%%~dpf"
			"%powershellpath%" write-host -fore White -back DarkGray Working in: """"'!lastdir!'""""
			)
		)

		set "rpctarget=!lastdir!%%~nf.rpy"
		set "rpcsource=%%~ff"

		if exist "!rpctarget!" (
			set /a countnodecomp+=1
			echo "%%~nf.rpy" already exists - skipped
		) else (
			set /a countdecomp+=1
  		echo    + Decompiling "!rpcsource!" - %%~zf bytes >> unren.log
 	  	"%pythondir%python.exe" -O "%unrpycpy%" --init-offset "!rpcsource!"

			if exist "!rpctarget!" (
        set /a countdecompsuccess+=1
      ) else (
        echo "!rpctarget!" not found - probably did not decompile >> unren.log
      )
		)
	)
)
echo.

REM Offer a decompilation summary
set /a countdecompfailed=%countdecomp%-%countdecompsuccess%
set "countprefix="

"%powershellpath%" write-host 'Summary: ' -fore DarkYellow -NoNewline
if NOT %countdecompsuccess% == 0 (
	if %countdecompsuccess% == 1 (
		set "filestr=file"
	) else (
		set "filestr=files"
	)
  "%powershellpath%" write-host ^(!countdecompsuccess!^) -fore White -NoNewline
  "%powershellpath%" write-host ' !filestr! newly decompiled ' -fore DarkYellow -NoNewline
	set "countprefix=, "
)
if NOT %countdecompfailed% == 0 (
	if %countdecompfailed% == 1 (
		set "filestr=file"
		set "existstr=exists"
	) else (
		set "filestr=files"
		set "existstr=exist"
	)
  "%powershellpath%" write-host ^(!countprefix!!countdecompfailed!^) -fore White -NoNewline
  "%powershellpath%" write-host ' !filestr! failed to decompile ' -fore DarkYellow -NoNewline
)
if NOT %countnodecomp% == 0 (
	if %countnodecomp% == 1 (
		set "filestr=file"
		set "existstr=exists"
	) else (
		set "filestr=files"
		set "existstr=exist"
	)
  "%powershellpath%" write-host ^(!countprefix!!countnodecomp!^) -fore White -NoNewline
  "%powershellpath%" write-host ' decompiled !filestr! already !existstr!' -fore DarkYellow -NoNewline
)
"%powershellpath%" write-host ' '
echo.
goto finish
REM --------------------------------------------------------------------------------
REM Clean up and return to our original working directory
REM --------------------------------------------------------------------------------
REM echo   Cleaning up temporary files...
if exist "%unrpycpy%" (
  del "%unrpycpy%"
)
if exist "%unrpycpy%o" (
  del "%unrpycpy%o"
)
if exist "%decompcab%.tmp" (
  del "%decompcab%.tmp"
)
if exist "%decompcab%" (
  del "%decompcab%"
)
if exist "%deobfuscate%" (
  del "%deobfuscate%"
)
if exist "%deobfuscate%o" (
  del "%deobfuscate%o"
)
if exist "%decompilerdir%" (
  rmdir /Q /S "%decompilerdir%"
)
if exist "%maindir%__pycache__" (
  rmdir /Q /S "%maindir%__pycache__"
)
echo.

if not "%option%" == "8" (
	goto finish
)

:console
REM --------------------------------------------------------------------------------
REM Drop our console/dev mode enabler into the game folder
REM --------------------------------------------------------------------------------
echo   Creating Developer/Console file...
set "consolefile=%gamedir%unren-dev.rpy"
if exist "%consolefile%" (
	del "%consolefile%"
)

echo init 999 python:>> "%consolefile%"
echo   config.developer = True>> "%consolefile%"
echo   config.console = True>> "%consolefile%"

echo    + Console: SHIFT+O
echo    + Dev Menu: SHIFT+D
echo.

:consoleend
if "%option%" == "7" (
	goto quick
)
if "%option%" == "8" (
	goto quick
)
goto finish

:quick
REM --------------------------------------------------------------------------------
REM Drop our Quick Save/Load file into the game folder
REM --------------------------------------------------------------------------------
echo   Creating Quick Save/Quick Load file...
set "quickfile=%gamedir%unren-quick.rpy"
if exist "%quickfile%" (
	del "%quickfile%"
)

echo init 999 python:>> "%quickfile%"
echo   try:>> "%quickfile%"
echo     config.underlay[0].keymap['quickSave'] = QuickSave()>> "%quickfile%"
echo     config.keymap['quickSave'] = '%quicksavekey%'>> "%quickfile%"
echo     config.underlay[0].keymap['quickLoad'] = QuickLoad()>> "%quickfile%"
echo     config.keymap['quickLoad'] = '%quickloadkey%'>> "%quickfile%"
echo   except:>> "%quickfile%"
echo     pass>> "%quickfile%"

echo    Default hotkeys:
echo    + Quick Save: F5
echo    + Quick Load: F9
echo.

if "%option%" == "7" (
	goto skip
)
if "%option%" == "8" (
	goto skip
)
goto finish


:skip
REM --------------------------------------------------------------------------------
REM Drop our skip file into the game folder
REM --------------------------------------------------------------------------------
echo   Creating skip file...
set "skipfile=%gamedir%unren-skip.rpy"
if exist "%skipfile%" (
	del "%skipfile%"
)

echo init 999 python:>> "%skipfile%"
echo   _preferences.skip_unseen = True>> "%skipfile%"
echo   renpy.game.preferences.skip_unseen = True>> "%skipfile%"
echo   renpy.config.allow_skipping = True>> "%skipfile%"
echo   renpy.config.fast_skipping = True>> "%skipfile%"

echo    + You can now skip all text using TAB and CTRL keys
echo.

if "%option%" == "7" (
	goto rollback
)
if "%option%" == "8" (
	goto rollback
)
goto finish

:rollback
REM --------------------------------------------------------------------------------
REM Drop our rollback file into the game folder
REM --------------------------------------------------------------------------------
echo   Creating rollback file...
set "rollbackfile=%gamedir%unren-rollback.rpy"
if exist "%rollbackfile%" (
	del "%rollbackfile%"
)

echo init 999 python:>> "%rollbackfile%"
echo   renpy.config.rollback_enabled = True>> "%rollbackfile%"
echo   renpy.config.hard_rollback_limit = 256>> "%rollbackfile%"
echo   renpy.config.rollback_length = 256>> "%rollbackfile%"
echo   def unren_noblock( *args, **kwargs ):>> "%rollbackfile%"
echo     return>> "%rollbackfile%"
echo   renpy.block_rollback = unren_noblock>> "%rollbackfile%"
echo   try:>> "%rollbackfile%"
echo     config.keymap['rollback'] = [ 'K_PAGEUP', 'repeat_K_PAGEUP', 'K_AC_BACK', 'mousedown_4' ]>> "%rollbackfile%"
echo   except:>> "%rollbackfile%"
echo     pass>> "%rollbackfile%"

echo    + You can now rollback using the scrollwheel
echo.

:finish
REM --------------------------------------------------------------------------------
REM We are done
REM --------------------------------------------------------------------------------
echo  ----------------------------------------------------
echo.
echo    Finished!
echo.
echo    Enter "1" to go back to the menu, or any other
set /p exitoption=.   key to exit: 
echo.
echo  ----------------------------------------------------
echo.
if "%exitoption%"=="1" goto menu

:finishdefault
echo End UnRen run >> unren.log
echo --------------------------------- >> unren.log

REM Restore the original codepage
chcp %cporig%>nul

endlocal
rem exit