{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"AssetStudioGUI/0.16.47": {"dependencies": {"AssetStudio": "0.16.47", "AssetStudioUtility": "0.16.47", "Newtonsoft.Json": "13.0.1", "OpenTK": "4.6.7", "OpenTK.WinForms": "*******"}, "runtime": {"AssetStudioGUI.dll": {}}}, "K4os.Compression.LZ4/1.2.16": {"runtime": {"lib/net5.0/K4os.Compression.LZ4.dll": {"assemblyVersion": "1.2.16.0", "fileVersion": "1.2.16.0"}}}, "Microsoft.NETCore.Platforms/1.1.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Mono.Cecil/0.11.3": {"runtime": {"lib/netstandard2.0/Mono.Cecil.Mdb.dll": {"assemblyVersion": "0.11.3.0", "fileVersion": "0.11.3.0"}, "lib/netstandard2.0/Mono.Cecil.Pdb.dll": {"assemblyVersion": "0.11.3.0", "fileVersion": "0.11.3.0"}, "lib/netstandard2.0/Mono.Cecil.Rocks.dll": {"assemblyVersion": "0.11.3.0", "fileVersion": "0.11.3.0"}, "lib/netstandard2.0/Mono.Cecil.dll": {"assemblyVersion": "0.11.3.0", "fileVersion": "0.11.3.0"}}}, "Newtonsoft.Json/13.0.1": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "13.0.0.0", "fileVersion": "13.0.1.25517"}}}, "OpenTK/4.6.7": {"dependencies": {"OpenTK.Compute": "4.6.7", "OpenTK.Core": "4.6.7", "OpenTK.Graphics": "4.6.7", "OpenTK.Input": "4.6.7", "OpenTK.Mathematics": "4.6.7", "OpenTK.OpenAL": "4.6.7", "OpenTK.Windowing.Common": "4.6.7", "OpenTK.Windowing.Desktop": "4.6.7", "OpenTK.Windowing.GraphicsLibraryFramework": "4.6.7"}}, "OpenTK.Compute/4.6.7": {"runtime": {"lib/netcoreapp3.1/OpenTK.Compute.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "OpenTK.Core/4.6.7": {"runtime": {"lib/netstandard2.1/OpenTK.Core.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.0.0.0"}}}, "OpenTK.Graphics/4.6.7": {"dependencies": {"OpenTK.Core": "4.6.7", "OpenTK.Mathematics": "4.6.7"}, "runtime": {"lib/netcoreapp3.1/OpenTK.Graphics.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.0.0.0"}}}, "OpenTK.Input/4.6.7": {"runtime": {"lib/netstandard2.0/OpenTK.Input.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.0.0.0"}}}, "OpenTK.Mathematics/4.6.7": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "5.0.0"}, "runtime": {"lib/netcoreapp3.1/OpenTK.Mathematics.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.0.0.0"}}}, "OpenTK.OpenAL/4.6.7": {"dependencies": {"OpenTK.Core": "4.6.7", "OpenTK.Mathematics": "4.6.7"}, "runtime": {"lib/netcoreapp3.1/OpenTK.OpenAL.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.0.0.0"}}}, "OpenTK.redist.glfw/3.3.0-pre20200830200122": {"runtimeTargets": {"runtimes/linux-x64/native/libglfw.so.3.3": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx-x64/native/libglfw.3.dylib": {"rid": "osx-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/glfw3.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/glfw3.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "OpenTK.Windowing.Common/4.6.7": {"dependencies": {"OpenTK.Core": "4.6.7", "OpenTK.Mathematics": "4.6.7"}, "runtime": {"lib/netcoreapp3.1/OpenTK.Windowing.Common.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.0.0.0"}}}, "OpenTK.Windowing.Desktop/4.6.7": {"dependencies": {"OpenTK.Core": "4.6.7", "OpenTK.Mathematics": "4.6.7", "OpenTK.Windowing.Common": "4.6.7", "OpenTK.Windowing.GraphicsLibraryFramework": "4.6.7"}, "runtime": {"lib/netcoreapp3.1/OpenTK.Windowing.Desktop.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.0.0.0"}}}, "OpenTK.Windowing.GraphicsLibraryFramework/4.6.7": {"dependencies": {"OpenTK.Core": "4.6.7", "OpenTK.redist.glfw": "3.3.0-pre20200830200122"}, "runtime": {"lib/netcoreapp3.1/OpenTK.Windowing.GraphicsLibraryFramework.dll": {"assemblyVersion": "4.0.0.0", "fileVersion": "4.0.0.0"}}}, "runtime.native.System/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "runtime.native.System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "SixLabors.Fonts/1.0.0-beta15": {"dependencies": {"System.IO.Compression": "4.3.0", "System.IO.UnmanagedMemoryStream": "4.3.0", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "5.0.0", "System.Threading.Tasks.Parallel": "4.3.0", "System.ValueTuple": "4.5.0"}, "runtime": {"lib/netstandard2.1/SixLabors.Fonts.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SixLabors.ImageSharp/1.0.3": {"runtime": {"lib/netcoreapp3.1/SixLabors.ImageSharp.dll": {"assemblyVersion": "*******", "fileVersion": "1.0.3.0"}}}, "SixLabors.ImageSharp.Drawing/1.0.0-beta13": {"dependencies": {"SixLabors.Fonts": "1.0.0-beta15", "SixLabors.ImageSharp": "1.0.3"}, "runtime": {"lib/netcoreapp3.1/SixLabors.ImageSharp.Drawing.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.Buffers/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Collections.Concurrent/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.Tracing/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.Compression/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Buffers": "4.3.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.IO.Compression": "4.3.0"}}, "System.IO.FileSystem.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.IO.UnmanagedMemoryStream/4.3.0": {"dependencies": {"System.Buffers": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Numerics.Vectors/4.5.0": {}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks.Parallel/4.3.0": {"dependencies": {"System.Collections.Concurrent": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.ValueTuple/4.5.0": {}, "AssetStudio/0.16.47": {"dependencies": {"K4os.Compression.LZ4": "1.2.16"}, "runtime": {"AssetStudio.dll": {}}}, "AssetStudio.PInvoke/0.16.47": {"runtime": {"AssetStudio.PInvoke.dll": {}}}, "AssetStudioFBXWrapper/0.16.47": {"dependencies": {"AssetStudio": "0.16.47", "AssetStudio.PInvoke": "0.16.47"}, "runtime": {"AssetStudioFBXWrapper.dll": {}}}, "AssetStudioUtility/0.16.47": {"dependencies": {"AssetStudio": "0.16.47", "AssetStudio.PInvoke": "0.16.47", "AssetStudioFBXWrapper": "0.16.47", "Mono.Cecil": "0.11.3", "SixLabors.ImageSharp.Drawing": "1.0.0-beta13", "Texture2DDecoderWrapper": "0.16.47"}, "runtime": {"AssetStudioUtility.dll": {}}}, "Texture2DDecoderWrapper/0.16.47": {"dependencies": {"AssetStudio.PInvoke": "0.16.47"}, "runtime": {"Texture2DDecoderWrapper.dll": {}}}, "OpenTK.WinForms/*******": {"runtime": {"OpenTK.WinForms.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}}}, "libraries": {"AssetStudioGUI/0.16.47": {"type": "project", "serviceable": false, "sha512": ""}, "K4os.Compression.LZ4/1.2.16": {"type": "package", "serviceable": true, "sha512": "sha512-XLNQWNayxeMgd1gv0s6kZywM11kww7rTzu3nPGh8fQNblHGbFt79LC1Sk1/QQ8DIJb2Qfl2p+WlLIOWCSuyi8w==", "path": "k4os.compression.lz4/1.2.16", "hashPath": "k4os.compression.lz4.1.2.16.nupkg.sha512"}, "Microsoft.NETCore.Platforms/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A==", "path": "microsoft.netcore.platforms/1.1.0", "hashPath": "microsoft.netcore.platforms.1.1.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Mono.Cecil/0.11.3": {"type": "package", "serviceable": true, "sha512": "sha512-DNYE+io5XfEE8+E+5padThTPHJARJHbz1mhbhMPNrrWGKVKKqj/KEeLvbawAmbIcT73NuxLV7itHZaYCZcVWGg==", "path": "mono.cecil/0.11.3", "hashPath": "mono.cecil.0.11.3.nupkg.sha512"}, "Newtonsoft.Json/13.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ppPFpBcvxdsfUonNcvITKqLl3bqxWbDCZIzDWHzjpdAHRFfZe0Dw9HmA0+za13IdyrgJwpkDTDA9fHaxOrt20A==", "path": "newtonsoft.json/13.0.1", "hashPath": "newtonsoft.json.13.0.1.nupkg.sha512"}, "OpenTK/4.6.7": {"type": "package", "serviceable": true, "sha512": "sha512-vdVQ7l0U506DZ+abpmgPAOutOnBv02+ao/OZqeu0r1BKYYaf9Qb8dFDE+DWRo2fREDku7ib+RQAngKvG5eJ7NA==", "path": "opentk/4.6.7", "hashPath": "opentk.4.6.7.nupkg.sha512"}, "OpenTK.Compute/4.6.7": {"type": "package", "serviceable": true, "sha512": "sha512-NwiFKORIPxTW2w5bdZwli8uuB6SfNhniQ68EsSONf07iMKsYRl9knlMWGC0yzvCTfA4xgoKh/Heu0bJ1PV4OpQ==", "path": "opentk.compute/4.6.7", "hashPath": "opentk.compute.4.6.7.nupkg.sha512"}, "OpenTK.Core/4.6.7": {"type": "package", "serviceable": true, "sha512": "sha512-g6qtoOop+RuyLRhsQ6Qxl5LVfhbMZmRM/RCL0WL/MbIfHXKpCOaNsU2JAuiKTVB9ULKsIq3lyLZmcwKKWIRprQ==", "path": "opentk.core/4.6.7", "hashPath": "opentk.core.4.6.7.nupkg.sha512"}, "OpenTK.Graphics/4.6.7": {"type": "package", "serviceable": true, "sha512": "sha512-czDA/alXa8zVkEZy+Ciwnh5haDLKrkjP5TZjrh0W0fayuDAGiDgUGPupHpfhAH5EZOH0PPKIiLsmJukhub/hqA==", "path": "opentk.graphics/4.6.7", "hashPath": "opentk.graphics.4.6.7.nupkg.sha512"}, "OpenTK.Input/4.6.7": {"type": "package", "serviceable": true, "sha512": "sha512-cE<PERSON>DE9xuV12hLg44mwCa6LnfOs3sG1LFo42hZajdJ1DTL/SUhDjJeYN/ttKfMy9SUHafweR0FMqkfJasHjuiA==", "path": "opentk.input/4.6.7", "hashPath": "opentk.input.4.6.7.nupkg.sha512"}, "OpenTK.Mathematics/4.6.7": {"type": "package", "serviceable": true, "sha512": "sha512-hRQCx0WVIzpl5QL/HoaZOpz9INbrfrikFQTXnzWUwQD0vDzgrMrmUVRdjsn4WnGgRI+KV19zUK8iwHUDGavU+g==", "path": "opentk.mathematics/4.6.7", "hashPath": "opentk.mathematics.4.6.7.nupkg.sha512"}, "OpenTK.OpenAL/4.6.7": {"type": "package", "serviceable": true, "sha512": "sha512-z5AWwfflhQIJgUMkJr3kdr0YaaBqYIyM1jJ0ppinDR5VI1i2pmDZ4Pom9d56yng/xXAkxr9j3FLACZ3zaTNTaw==", "path": "opentk.openal/4.6.7", "hashPath": "opentk.openal.4.6.7.nupkg.sha512"}, "OpenTK.redist.glfw/3.3.0-pre20200830200122": {"type": "package", "serviceable": true, "sha512": "sha512-EKE9pJCFHScbqOv/t0zG1h5Eh24nWr44SXYnpYtAWDhJhzAoUoI8TwDRXGvTGhDeCBp8ONEaVsq9xRC+OQIxQA==", "path": "opentk.redist.glfw/3.3.0-pre20200830200122", "hashPath": "opentk.redist.glfw.3.3.0-pre20200830200122.nupkg.sha512"}, "OpenTK.Windowing.Common/4.6.7": {"type": "package", "serviceable": true, "sha512": "sha512-xLdsegwi1ggUEWGjvOySFioZf1fRNmCsVxFuW5+0wtmBzLbRdedxQeumKppkioJHhsDe8puM2iokjjYJq+9vkQ==", "path": "opentk.windowing.common/4.6.7", "hashPath": "opentk.windowing.common.4.6.7.nupkg.sha512"}, "OpenTK.Windowing.Desktop/4.6.7": {"type": "package", "serviceable": true, "sha512": "sha512-I8zxitiMIoxPacMlwrkbjLNYDw+VcdAYKwF6ECo5I5JZZELHizBJXO9txv4JHrQC3QLWtfaYAORHocSqd6h09Q==", "path": "opentk.windowing.desktop/4.6.7", "hashPath": "opentk.windowing.desktop.4.6.7.nupkg.sha512"}, "OpenTK.Windowing.GraphicsLibraryFramework/4.6.7": {"type": "package", "serviceable": true, "sha512": "sha512-GMFH6nlGll+UM37al3BrjqczyY9KDigHG5H1Tkgf186Z2wRXX3Thx1cOFNaDwBIB7iva73bA0a+cQHgKfV/3wg==", "path": "opentk.windowing.graphicslibraryframework/4.6.7", "hashPath": "opentk.windowing.graphicslibraryframework.4.6.7.nupkg.sha512"}, "runtime.native.System/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "path": "runtime.native.system/4.3.0", "hashPath": "runtime.native.system.4.3.0.nupkg.sha512"}, "runtime.native.System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-INBPonS5QPEgn7naufQFXJEp3zX6L4bwHgJ/ZH78aBTpeNfQMtf7C6VrAFhlq2xxWBveIOWyFzQjJ8XzHMhdOQ==", "path": "runtime.native.system.io.compression/4.3.0", "hashPath": "runtime.native.system.io.compression.4.3.0.nupkg.sha512"}, "SixLabors.Fonts/1.0.0-beta15": {"type": "package", "serviceable": true, "sha512": "sha512-Gk4KMoJtbsA5/bp40kIr82NpBuNPQqFVz0RcwhFDDGCwdGZrDAV2q/pq3b14eJ/kQzVYBYQZ9hc5h9jbH2rURA==", "path": "sixlabors.fonts/1.0.0-beta15", "hashPath": "sixlabors.fonts.1.0.0-beta15.nupkg.sha512"}, "SixLabors.ImageSharp/1.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-oRmZEITad+0tL22ZfAbLps6doDV+Z0/oH0dtXLpIBwn1sgai3QxxDQPjohHiDeUp/CTzRKVrFFbc4ZITnL2UBg==", "path": "sixlabors.imagesharp/1.0.3", "hashPath": "sixlabors.imagesharp.1.0.3.nupkg.sha512"}, "SixLabors.ImageSharp.Drawing/1.0.0-beta13": {"type": "package", "serviceable": true, "sha512": "sha512-YvGptEWWc9o6V8NMJ99x1283pH9T50zIvckSfdGr/apbIDlI2/VuBEHuhUY5MHTqCQyeSiaAG/t2uXk3Vff/7g==", "path": "sixlabors.imagesharp.drawing/1.0.0-beta13", "hashPath": "sixlabors.imagesharp.drawing.1.0.0-beta13.nupkg.sha512"}, "System.Buffers/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ratu44uTIHgeBeI0dE8DWvmXVBSo4u7ozRZZHOMmK/JPpYyo0dAfgSiHlpiObMQ5lEtEyIXA40sKRYg5J6A8uQ==", "path": "system.buffers/4.3.0", "hashPath": "system.buffers.4.3.0.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Concurrent/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "path": "system.collections.concurrent/4.3.0", "hashPath": "system.collections.concurrent.4.3.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "path": "system.diagnostics.tracing/4.3.0", "hashPath": "system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.Compression/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YHndyoiV90iu4iKG115ibkhrG+S3jBm8Ap9OwoUAzO5oPDAWcr0SFwQFm0HjM8WkEZWo0zvLTyLmbvTkW1bXgg==", "path": "system.io.compression/4.3.0", "hashPath": "system.io.compression.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "path": "system.io.filesystem.primitives/4.3.0", "hashPath": "system.io.filesystem.primitives.4.3.0.nupkg.sha512"}, "System.IO.UnmanagedMemoryStream/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Ck9nPyj78bYYTAXxvO32p5insYbinLbNS2AjPX1gyTUS/i0hytzKkscIK3KynfmTyrw2boywHGIH5VOkut7/bQ==", "path": "system.io.unmanagedmemorystream/4.3.0", "hashPath": "system.io.unmanagedmemorystream.4.3.0.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZD9TMpsmYJLrxbbmdvhwt9YEgG5WntEnZ/d1eH8JBX9LBp+Ju8BSBhUGbZMNVHHomWo2KVImJhTDl2hIgw/6MA==", "path": "system.runtime.compilerservices.unsafe/5.0.0", "hashPath": "system.runtime.compilerservices.unsafe.5.0.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Parallel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-8Z/cgCIBlSRjOfEcj9mzrcYueyajgN8Y1RielC//g547IbUjf8VoCUhTB02urmyXfaWdn9FBosWX54vwZTpDcg==", "path": "system.threading.tasks.parallel/4.3.0", "hashPath": "system.threading.tasks.parallel.4.3.0.nupkg.sha512"}, "System.ValueTuple/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-okurQJO6NRE/apDIP23ajJ0hpiNmJ+f0BwOlB/cSqTLQlw5upkf+5+96+iG2Jw40G1fCVCyPz/FhIABUjMR+RQ==", "path": "system.valuetuple/4.5.0", "hashPath": "system.valuetuple.4.5.0.nupkg.sha512"}, "AssetStudio/0.16.47": {"type": "project", "serviceable": false, "sha512": ""}, "AssetStudio.PInvoke/0.16.47": {"type": "project", "serviceable": false, "sha512": ""}, "AssetStudioFBXWrapper/0.16.47": {"type": "project", "serviceable": false, "sha512": ""}, "AssetStudioUtility/0.16.47": {"type": "project", "serviceable": false, "sha512": ""}, "Texture2DDecoderWrapper/0.16.47": {"type": "project", "serviceable": false, "sha512": ""}, "OpenTK.WinForms/*******": {"type": "reference", "serviceable": false, "sha512": ""}}}