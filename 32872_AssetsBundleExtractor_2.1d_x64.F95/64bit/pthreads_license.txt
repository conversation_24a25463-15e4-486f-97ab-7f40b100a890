Pthreads-win32 - POSIX Threads Library for Win32
Copyright(C) 1998 John <PERSON>
Copyright(C) 1999,2012 Pthreads-win32 contributors

Homepage1: http://sourceware.org/pthreads-win32/
Homepage2: http://sourceforge.net/projects/pthreads4w/

The current list of contributors is contained
in the file CONTRIBUTORS included with the source
code distribution. The list can also be seen at the
following World Wide Web location:
http://sources.redhat.com/pthreads-win32/contributors.html

This library is free software; you can redistribute it and/or
modify it under the terms of the GNU Lesser General Public
License as published by the Free Software Foundation; either
version 2 of the License, or (at your option) any later version.

This library is distributed in the hope that it will be useful,
but WITHOUT ANY WARRANTY; without even the implied warranty of
ME<PERSON><PERSON><PERSON><PERSON>ILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
Lesser General Public License for more details.

You should have received a copy of the GNU Lesser General Public
License along with this library in the file pthreads_lgpl.txt;
if not, write to the Free Software Foundation, Inc.,
59 Temple Place - Suite 330, Boston, MA 02111-1307, USA