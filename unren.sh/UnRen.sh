#!/usr/bin/env bash
# This file:
#
#  Unpacking and decompiling games made with Ren'Py Visual Engines
#
# Usage:
#
#  ./UnRen.sh [OPTIONAL-path-to-games-main-folder-or-bundle-app]
#  (or run it from file manager, e.g. <PERSON><PERSON>)
#
# Bash port of Windows script UnRen-ultrahack(vXX).bat by VepsrP
# https://github.com/VepsrP/UnRen-<PERSON>-mod-
# (https://f95zone.to/threads/92717) 
#

# WARNING: Do not change any codes outside of the BEGIN and END regions listed below.

# ------------------------------------------------------------------------------
# BEGIN - Configuration
# ------------------------------------------------------------------------------
# You can change the keyboard key for quicksave and quick load in Ren'Py games
# see: http://www.pygame.org/docs/ref/key.html#key-constants-label
quicksavekey="K_F5"
quickloadkey="K_F9"
# -----------------------------------------------------------------------------
# END - Configuration
# -----------------------------------------------------------------------------

# The following variables are Base64 encoded strings for unrpyc and rpatool
# rpatool by Shizmob 9a58396 2019-02-22T17:31:07.000Z
# https://github.com/Shizmob/rpatool
#
# unrpyc by CensoredUsername
# https://github.com/CensoredUsername/unrpyc)
# Edited to remove multiprocessing and adjust output spacing 44febb0 2019-10-07T07:06:47.000Z
# https://github.com/F95Sam/unrpyc

b64unrpyc='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'
b64rpatool='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'
b64console='aW5pdCA5OTkgcHl0aG9uOg0KICAgIGNvbmZpZy5kZXZlbG9wZXIgPSBUcnVlDQogICAgY29uZmlnLmNvbnNvbGUgPSBUcnVl'
b64skip='aW5pdCA5OTkgcHl0aG9uOg0KICAgIF9wcmVmZXJlbmNlcy5za2lwX3Vuc2VlbiA9IFRydWUNCiAgICByZW5weS5nYW1lLnByZWZlcmVuY2VzLnNraXBfdW5zZWVuID0gVHJ1ZQ0KICAgIHJlbnB5LmNvbmZpZy5hbGxvd19za2lwcGluZyA9IFRydWUNCiAgICByZW5weS5jb25maWcuZmFzdF9za2lwcGluZyA9IFRydWUNCiAgICB0cnk6DQogICAgICAgIGNvbmZpZy5rZXltYXBbJ3NraXAnXSA9IFsgJ0tfTENUUkwnLCAnS19SQ1RSTCcgXQ0KICAgIGV4Y2VwdDoNCiAgICAgICAgcGFzcw0K'
b64rollback='aW5pdCA5OTkgcHl0aG9uOg0KICAgIHJlbnB5LmNvbmZpZy5yb2xsYmFja19lbmFibGVkID0gVHJ1ZQ0KICAgIHJlbnB5LmNvbmZpZy5oYXJkX3JvbGxiYWNrX2xpbWl0ID0gMjU2DQogICAgcmVucHkuY29uZmlnLnJvbGxiYWNrX2xlbmd0aCA9IDI1Ng0KICAgIGRlZiB1bnJlbl9ub2Jsb2NrKCAqYXJncywgKiprd2FyZ3MgKToNCiAgICAgICAgcmV0dXJuDQogICAgcmVucHkuYmxvY2tfcm9sbGJhY2sgPSB1bnJlbl9ub2Jsb2NrDQogICAgdHJ5Og0KICAgICAgICBjb25maWcua2V5bWFwWydyb2xsYmFjayddID0gWyAnS19QQUdFVVAnLCAncmVwZWF0X0tfUEFHRVVQJywgJ0tfQUNfQkFDSycsICdtb3VzZWRvd25fNCcgXQ0KICAgIGV4Y2VwdDoNCiAgICAgICAgcGFzcw=='

args="$*"
version="0.17.0 for Mac & Linux"
minpyver=2007018

trap 'printf -- %s\\n "interrupted..."; exit 1' INT TERM

Die() {
  printf -- '%s\n\n' "$*" >&2
  exit 1
}

Cleanup() {
  [[ -e "$rpatool" ]] && rm -fr "$rpatool"*
  [[ -e "$unrpyc" ]] && rm -fr "$unrpyc"* \
    "$app"/deobfuscate.py* \
    "$app"/__pycache__ \
    "$app"/decompiler
}

Extract() {
  local errortemp
  rpatool="$app"/rpatool.py
  printf -- %s "$b64rpatool" | base64 --decode >"$rpatool"
  trap 'Cleanup' RETURN EXIT
  pushd "$game" >/dev/null || return
  errortemp=temp_$(date "+%S%s")
  "$python" ${pyargs+"$pyargs"} "$rpatool" . 2>"$errortemp"
  awk '!/^Co.*exec_prefix/{print "  > "$0}' "$errortemp"
  [[ -f $errortemp ]] && rm "$errortemp"
  popd >/dev/null || return
}

Decompile() {
  local errortemp opt=(--init-offset)
  [[ $1 ]] && opt=("${opt[@]}" "$1")
  unrpyc="$app/unrpyc.py"
  printf -- %s "$b64unrpyc" | base64 --decode | tar -xjf - --directory "$app" --
  trap 'Cleanup' RETURN EXIT
  pushd "$game" >/dev/null || return
  errortemp=temp_$(date "+%S%s")
  "$python" ${pyargs+"$pyargs"} "${unrpyc}" "${opt[@]}" . 2>"$errortemp"
  awk '!/^Co.*exec_prefix/{print "  > "$0}' "$errortemp"
  [[ -f $errortemp ]] && rm "$errortemp"
  popd >/dev/null || return
}

Console() {
  printf -- %s "${b64console}" | base64 --decode >"$game"/unren-dev.rpy
  echo "Added:"
  echo "  + Console: SHIFT+O"
  echo "  + Dev Menu: SHIFT+D"
}

Quick() {
  printf -- "init 999 python:
    try:
        config.underlay[0].keymap['quickSave'] = QuickSave()
        config.keymap['quickSave'] = '%s'
        config.underlay[0].keymap['quickLoad'] = QuickLoad()
        config.keymap['quickLoad'] = '%s'
    except:
        pass\n" "$quicksavekey" "$quickloadkey" >"$game/unren-quick.rpy"
  [[ $ch = 4 ]] && echo "Added:"
  echo "  + Quick Save: ${quicksavekey#K_}"
  echo "  + Quick Load: ${quickloadkey#K_}"
}

Skip() {
  printf -- %s "${b64skip}" | base64 --decode >"$game"/unren-skip.rpy
  [[ $ch = 5 ]] && echo "Added:"
  echo "  + You can now skip all text using TAB or CTRL keys"
}

Rollback() {
  printf -- %s "${b64rollback}" | base64 --decode >"$game"/unren-rollback.rpy
  [[ $ch = 6 ]] && echo "Added:"
  echo "  + You can now rollback using the scrollwheel"
}

Finished() {
  local rorq=
  echo "Done!"
  read -r -n1 -p "Press any key to back to main menu, or (q) to quit UnRen >" rorq
  echo
  if [[ $rorq = "q" ]]; then
    echo "Bye..."
    exit 0
  else
    Menu
  fi
}

Menu() {
  while true; do
    echo "Available Options:"
    echo "   1) Extract RPA packages (in game folder)"
    echo "   2) Decompile rpyc files (in game folder)"
    echo
    echo "   3) Enable Console and Developer Menu"
    echo "   4) Enable Quick Save and Quick Load"
    echo "   5) Force enable skipping of unseen content"
    echo "   6) Force enable rollback (scroll wheel)"
    echo
    echo "   7) Options 3-6"
    echo "   8) Options 1-6"
    echo "   9) Options 1-6 + Deobfuscate rpyc"
    echo
    echo "   0) Decompile rpyc files and overwrite existing RPY files"
    echo
    echo "   q) quit"
    echo
    while true; do
      read -r -n1 -p "Please select > " ch
      echo
      case $ch in
        0) Decompile --clobber ;;
        1) Extract ;;
        2) Decompile ;;
        3) Console ;;
        4) Quick ;;
        5) Skip ;;
        6) Rollback ;;
        7)
          Console
          Quick
          Skip
          Rollback
          ;;
        8)
          Extract
          Decompile
          Console
          Quick
          Skip
          Rollback
          ;;
        9)
          Extract
          Decompile --try-hard
          Console
          Quick
          Skip
          Rollback
          ;;
        q)
          echo "Bye..."
          exit 0
          ;;
        *)
          printf -v tryagain -- '\aEnter number 0-9, or q to Exit'
          printf -- '\a%s\n' "$tryagain"
          ;;
      esac
      [[ $tryagain != '' ]] || Finished
      tryagain=
    done
  done
}

Check() {
  # MacOS
  if [[ -e "$app"/Contents/Resources/autorun/renpy &&
      -e "$app"/Contents/Resources/autorun/game ]]; then
    # can't run game's python if quarantined and not in /Applications
    xattr -rd com.apple.quarantine "$app" 2>/dev/null
    game="$app/Contents/Resources/autorun/game"
    python="$(find "$app/Contents/MacOS" -type f -name 'python')"
    PYTHONPATH="$(find "$app" \( -path '*/MacOS/*' -or -path '*/Resources/lib/*' \) -type d -name 'encodings' -exec dirname -- {} \;)"
    app="$app/Contents/Resources/autorun"
  else
    [[ -e "$app/renpy" && -e "$app/game" ]] || Die "Unable to determined Ren'Py game or not. Exiting..."
    game="$app/game"
    python="$(find "$app/lib" -path "*$(uname -m)*" -type f -name 'python')"
    PYTHONPATH="$(find "$app/lib" -path '*python*' -type d -name 'encodings' -exec dirname -- {} \;)"
  fi
  [[ -e "$python" ]] || Die "Unable to locate python. Exiting..."
  gamepyver=$($python --version 2>&1 | awk '{gsub("[^[:digit:]]+"," ");printf("%d%03d%03d\n",$1,$2,$3)}')
  if [[ ${gamepyver::1} == 3 ]]; then
    [[ -d "$PYTHONPATH/encodings" ]] || Die "Unable to locate the game python's libraries. Exiting..."
    PYTHONHOME="$(dirname "$python")"
    PYTHONPATH="$PYTHONPATH:$PYTHONHOME"
    export PYTHONHOME PYTHONPATH
  fi
  ((gamepyver < minpyver)) && pyargs='-EO'
  # make sure the game's python is executables (_rarely_, obtained games from just rezipping under Windows)
  [[ -x "$python" ]] || chmod -f +x "$python"
  Menu
}

Main() {
  clear
  echo '   __  __      ____                    __   '
  echo '  / / / /___  / __ \___  ____    _____/ /_  '
  echo ' / / / / __ \/ /_/ / _ \/ __ \  / ___/ __ \ '
  echo '/ /_/ / / / / _, _/  __/ / / / (__  ) / / / '
  echo '\____/_/ /_/_/ |_|\___/_/ /_(_)____/_/ /_/  '
  echo "version:" "$version"
  echo '___________________________________________ '
  if [[ "$args" = "" ]]; then
    echo "Drag-n-drop the app/directory here, and press ENTER"
    read -r app
    eval "app=$app"
  else
    app="$args"
  fi
  echo
  # relative to absolute path
  if [[ -d $app ]]; then
    app="$(cd -P -- "$app" && pwd)"
  elif [[ -d $(dirname -- "$app") ]]; then
    app="$(cd -P -- "$(dirname -- "$app" 2>/dev/null)" && pwd)"/"$(basename "$app")"
  fi
  Check
}

Main
